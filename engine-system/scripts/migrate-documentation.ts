#!/usr/bin/env tsx

import * as fs from 'fs'
import * as path from 'path'

interface DocumentationMetadata {
    title?: string
    description?: string
    category?: string
    order?: number
    tags?: string[]
    author?: string
    version?: string
}

interface FileMapping {
    filename: string
    metadata: DocumentationMetadata
}

// Define metadata for each documentation file
const fileMetadata: FileMapping[] = [
    {
        filename: 'PROJECT_OVERVIEW.md',
        metadata: {
            title: 'Project Overview',
            description: 'Complete system overview including architecture, features, and core concepts of the Notification Engine',
            category: 'general',
            order: 1,
            tags: ['overview', 'architecture', 'getting-started'],
            author: 'Notification Engine Team',
            version: '1.0.0'
        }
    },
    {
        filename: 'API.md',
        metadata: {
            title: 'API Documentation',
            description: 'Complete REST API documentation with endpoints, request/response schemas, and practical examples',
            category: 'api',
            order: 1,
            tags: ['api', 'rest', 'endpoints', 'integration'],
            author: 'Notification Engine Team',
            version: '1.0.0'
        }
    },
    {
        filename: 'DEVELOPMENT.md',
        metadata: {
            title: 'Development Guide',
            description: 'Development environment setup, coding standards, and development workflows',
            category: 'development',
            order: 1,
            tags: ['development', 'setup', 'coding-standards', 'workflow'],
            author: 'Notification Engine Team',
            version: '1.0.0'
        }
    },
    {
        filename: 'DATABASE.md',
        metadata: {
            title: 'Database Schema',
            description: 'Complete database models, relationships, and data management strategies',
            category: 'architecture',
            order: 2,
            tags: ['database', 'schema', 'models', 'prisma'],
            author: 'Notification Engine Team',
            version: '1.0.0'
        }
    },
    {
        filename: 'QUEUES.md',
        metadata: {
            title: 'Queue System',
            description: 'BullMQ and RabbitMQ architecture, job processing, and monitoring',
            category: 'architecture',
            order: 3,
            tags: ['queues', 'bullmq', 'rabbitmq', 'jobs', 'processing'],
            author: 'Notification Engine Team',
            version: '1.0.0'
        }
    },
    {
        filename: 'TEMPLATES.md',
        metadata: {
            title: 'Template System',
            description: 'Handlebars templating system, layout inheritance, and template development',
            category: 'architecture',
            order: 4,
            tags: ['templates', 'handlebars', 'layouts', 'customization'],
            author: 'Notification Engine Team',
            version: '1.0.0'
        }
    },
    {
        filename: 'DEPLOYMENT.md',
        metadata: {
            title: 'Deployment Guide',
            description: 'Production deployment instructions, Docker setup, and environment configuration',
            category: 'deployment',
            order: 1,
            tags: ['deployment', 'production', 'docker', 'configuration'],
            author: 'Notification Engine Team',
            version: '1.0.0'
        }
    },
    {
        filename: 'TESTING.md',
        metadata: {
            title: 'Testing Guide',
            description: 'Unit testing, integration testing, and automated testing best practices',
            category: 'development',
            order: 2,
            tags: ['testing', 'unit-tests', 'integration-tests', 'jest'],
            author: 'Notification Engine Team',
            version: '1.0.0'
        }
    },
    {
        filename: 'TROUBLESHOOTING.md',
        metadata: {
            title: 'Troubleshooting Guide',
            description: 'Common issues, debugging techniques, and performance troubleshooting',
            category: 'troubleshooting',
            order: 1,
            tags: ['troubleshooting', 'debugging', 'issues', 'performance'],
            author: 'Notification Engine Team',
            version: '1.0.0'
        }
    },
    {
        filename: 'INTEGRATION_GUIDE.md',
        metadata: {
            title: 'Integration Guide',
            description: 'Step-by-step integration examples for email, Slack, and HubSpot channels',
            category: 'api',
            order: 2,
            tags: ['integration', 'examples', 'email', 'slack', 'hubspot'],
            author: 'Notification Engine Team',
            version: '1.0.0'
        }
    },
    {
        filename: 'CONTRIBUTING.md',
        metadata: {
            title: 'Contributing Guidelines',
            description: 'How to contribute to the project, code review process, and submission guidelines',
            category: 'development',
            order: 3,
            tags: ['contributing', 'guidelines', 'code-review', 'collaboration'],
            author: 'Notification Engine Team',
            version: '1.0.0'
        }
    },
    {
        filename: 'README.md',
        metadata: {
            title: 'Documentation Index',
            description: 'Main documentation index and navigation guide',
            category: 'general',
            order: 2,
            tags: ['index', 'navigation', 'overview'],
            author: 'Notification Engine Team',
            version: '1.0.0'
        }
    }
]

/**
 * Generate YAML frontmatter from metadata
 */
function generateFrontmatter(metadata: DocumentationMetadata): string {
    const lines = ['---']
    
    if (metadata.title) {
        lines.push(`title: "${metadata.title}"`)
    }
    
    if (metadata.description) {
        lines.push(`description: "${metadata.description}"`)
    }
    
    if (metadata.category) {
        lines.push(`category: ${metadata.category}`)
    }
    
    if (metadata.order) {
        lines.push(`order: ${metadata.order}`)
    }
    
    if (metadata.tags && metadata.tags.length > 0) {
        lines.push('tags:')
        metadata.tags.forEach(tag => {
            lines.push(`  - ${tag}`)
        })
    }
    
    if (metadata.author) {
        lines.push(`author: "${metadata.author}"`)
    }
    
    if (metadata.version) {
        lines.push(`version: "${metadata.version}"`)
    }
    
    lines.push('---')
    lines.push('') // Empty line after frontmatter
    
    return lines.join('\n')
}

/**
 * Check if file already has frontmatter
 */
function hasFrontmatter(content: string): boolean {
    return content.trim().startsWith('---')
}

/**
 * Migrate a single documentation file
 */
function migrateFile(filePath: string, metadata: DocumentationMetadata): void {
    try {
        const content = fs.readFileSync(filePath, 'utf-8')
        
        if (hasFrontmatter(content)) {
            console.log(`⏭️  Skipping ${path.basename(filePath)} - already has frontmatter`)
            return
        }
        
        const frontmatter = generateFrontmatter(metadata)
        const newContent = frontmatter + content
        
        // Create backup
        const backupPath = filePath + '.backup'
        fs.writeFileSync(backupPath, content)
        
        // Write new content
        fs.writeFileSync(filePath, newContent)
        
        console.log(`✅ Migrated ${path.basename(filePath)}`)
    } catch (error) {
        console.error(`❌ Error migrating ${path.basename(filePath)}:`, error)
    }
}

/**
 * Main migration function
 */
function migrateDocumentation(): void {
    const docsDir = path.join(__dirname, '..', 'docs')
    
    if (!fs.existsSync(docsDir)) {
        console.error('❌ Documentation directory not found:', docsDir)
        process.exit(1)
    }
    
    console.log('🚀 Starting documentation migration...')
    console.log(`📁 Documentation directory: ${docsDir}`)
    console.log('')
    
    let migratedCount = 0
    let skippedCount = 0
    let errorCount = 0
    
    fileMetadata.forEach(({ filename, metadata }) => {
        const filePath = path.join(docsDir, filename)
        
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️  File not found: ${filename}`)
            return
        }
        
        try {
            const content = fs.readFileSync(filePath, 'utf-8')
            
            if (hasFrontmatter(content)) {
                console.log(`⏭️  Skipping ${filename} - already has frontmatter`)
                skippedCount++
                return
            }
            
            migrateFile(filePath, metadata)
            migratedCount++
        } catch (error) {
            console.error(`❌ Error processing ${filename}:`, error)
            errorCount++
        }
    })
    
    console.log('')
    console.log('📊 Migration Summary:')
    console.log(`   ✅ Migrated: ${migratedCount} files`)
    console.log(`   ⏭️  Skipped: ${skippedCount} files`)
    console.log(`   ❌ Errors: ${errorCount} files`)
    console.log('')
    
    if (migratedCount > 0) {
        console.log('💡 Backup files created with .backup extension')
        console.log('💡 You can remove backup files after verifying the migration')
        console.log('')
        console.log('🔄 Next steps:')
        console.log('   1. Review the migrated files')
        console.log('   2. Update the documentation routes to use the unified system')
        console.log('   3. Test the new documentation system')
        console.log('   4. Remove backup files when satisfied')
    }
    
    if (errorCount > 0) {
        console.log('⚠️  Some files had errors during migration. Please review and fix manually.')
        process.exit(1)
    }
    
    console.log('✨ Migration completed successfully!')
}

/**
 * Clean up backup files
 */
function cleanupBackups(): void {
    const docsDir = path.join(__dirname, '..', 'docs')
    
    if (!fs.existsSync(docsDir)) {
        console.error('❌ Documentation directory not found:', docsDir)
        process.exit(1)
    }
    
    console.log('🧹 Cleaning up backup files...')
    
    const files = fs.readdirSync(docsDir)
    const backupFiles = files.filter(file => file.endsWith('.backup'))
    
    if (backupFiles.length === 0) {
        console.log('ℹ️  No backup files found')
        return
    }
    
    backupFiles.forEach(file => {
        const filePath = path.join(docsDir, file)
        fs.unlinkSync(filePath)
        console.log(`🗑️  Removed ${file}`)
    })
    
    console.log(`✅ Cleaned up ${backupFiles.length} backup files`)
}

// CLI interface
const command = process.argv[2]

switch (command) {
    case 'migrate':
        migrateDocumentation()
        break
    case 'cleanup':
        cleanupBackups()
        break
    case 'help':
    case '--help':
    case '-h':
        console.log('📚 Documentation Migration Tool')
        console.log('')
        console.log('Usage:')
        console.log('  npm run migrate-docs migrate  - Migrate documentation files')
        console.log('  npm run migrate-docs cleanup  - Clean up backup files')
        console.log('  npm run migrate-docs help     - Show this help')
        break
    default:
        console.log('❌ Invalid command. Use "migrate", "cleanup", or "help"')
        process.exit(1)
}
