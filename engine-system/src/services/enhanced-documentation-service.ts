import { marked } from 'marked'
import * as fs from 'fs'
import * as path from 'path'
import matter from 'gray-matter'
import Logger from '../utils/logger'
import {
    documentationConfig,
    isCacheEnabled,
    isLoggingEnabled,
} from '../config/documentation.config'

const logger = Logger.getInstance()

export interface DocumentationMetadata {
    title?: string
    description?: string
    category?: string
    order?: number
    tags?: string[]
    lastModified?: Date
    author?: string
    version?: string
}

export interface EnhancedDocumentationFile {
    path: string
    title: string
    content: string
    htmlContent: string
    lastModified: Date
    metadata: DocumentationMetadata
    category: string
    slug: string
    breadcrumbs: string[]
}

export interface DocumentationCategory {
    name: string
    slug: string
    description: string
    order: number
    files: EnhancedDocumentationFile[]
}

export interface EnhancedDocumentationIndex {
    files: EnhancedDocumentationFile[]
    categories: DocumentationCategory[]
    lastUpdated: Date
    navigation: NavigationItem[]
}

export interface NavigationItem {
    title: string
    path: string
    children?: NavigationItem[]
    category?: string
    order?: number
}

export class EnhancedDocumentationService {
    private static instance: EnhancedDocumentationService | null = null
    private cache: EnhancedDocumentationIndex | null = null
    private readonly projectRoot: string
    private readonly cacheTimeout = 5 * 60 * 1000 // 5 minutes

    private constructor() {
        this.projectRoot = path.resolve(__dirname, '../..')
        this.setupMarkedOptions()
    }

    public static getInstance(): EnhancedDocumentationService {
        if (!this.instance) {
            this.instance = new EnhancedDocumentationService()
        }
        return this.instance
    }

    private setupMarkedOptions(): void {
        marked.setOptions({
            gfm: true,
            breaks: false,
            headerIds: true,
            mangle: false,
        })
    }

    /**
     * Get all documentation with enhanced metadata and organization
     */
    public async getDocumentation(
        forceRefresh = false
    ): Promise<EnhancedDocumentationIndex> {
        if (!forceRefresh && this.cache && this.isCacheValid()) {
            return this.cache
        }

        try {
            const files = await this.discoverMarkdownFiles()
            const documentationFiles = await Promise.all(
                files.map((filePath) =>
                    this.parseEnhancedMarkdownFile(filePath)
                )
            )

            const validFiles = documentationFiles.filter(
                Boolean
            ) as EnhancedDocumentationFile[]
            const categories = this.organizeIntoCategories(validFiles)
            const navigation = this.generateNavigation(categories)

            this.cache = {
                files: validFiles,
                categories,
                navigation,
                lastUpdated: new Date(),
            }

            logger.info(
                `Enhanced documentation cache updated with ${this.cache.files.length} files in ${this.cache.categories.length} categories`
            )
            return this.cache
        } catch (error) {
            logger.error('Error loading enhanced documentation:', error)
            throw new Error('Failed to load documentation')
        }
    }

    /**
     * Get a specific documentation file by path or slug
     */
    public async getDocumentationFile(
        pathOrSlug: string
    ): Promise<EnhancedDocumentationFile | null> {
        const documentation = await this.getDocumentation()
        return (
            documentation.files.find(
                (file) =>
                    file.path.includes(pathOrSlug) ||
                    file.slug === pathOrSlug ||
                    path.basename(file.path) === path.basename(pathOrSlug)
            ) || null
        )
    }

    /**
     * Get files by category
     */
    public async getFilesByCategory(
        categorySlug: string
    ): Promise<EnhancedDocumentationFile[]> {
        const documentation = await this.getDocumentation()
        const category = documentation.categories.find(
            (cat) => cat.slug === categorySlug
        )
        return category ? category.files : []
    }

    /**
     * Search documentation content
     */
    public async searchDocumentation(
        query: string
    ): Promise<EnhancedDocumentationFile[]> {
        const documentation = await this.getDocumentation()
        const searchTerm = query.toLowerCase()

        return documentation.files.filter(
            (file) =>
                file.title.toLowerCase().includes(searchTerm) ||
                file.content.toLowerCase().includes(searchTerm) ||
                file.metadata.description?.toLowerCase().includes(searchTerm) ||
                file.metadata.tags?.some((tag) =>
                    tag.toLowerCase().includes(searchTerm)
                )
        )
    }

    /**
     * Parse markdown file with frontmatter support
     */
    private async parseEnhancedMarkdownFile(
        filePath: string
    ): Promise<EnhancedDocumentationFile | null> {
        try {
            const fileContent = fs.readFileSync(filePath, 'utf-8')
            const stats = fs.statSync(filePath)
            const relativePath = path.relative(this.projectRoot, filePath)

            // Parse frontmatter
            const { data: frontmatter, content } = matter(fileContent)

            // Extract title from frontmatter or first heading
            const title =
                frontmatter.title || this.extractTitle(content, filePath)

            // Generate slug from filename
            const slug = this.generateSlug(path.basename(filePath, '.md'))

            // Determine category from path or frontmatter
            const category =
                frontmatter.category || this.inferCategory(relativePath)

            // Generate breadcrumbs
            const breadcrumbs = this.generateBreadcrumbs(relativePath, category)

            // Convert markdown to HTML
            const htmlContent = await marked(content)

            const metadata: DocumentationMetadata = {
                title: frontmatter.title,
                description: frontmatter.description,
                category: frontmatter.category,
                order: frontmatter.order || 999,
                tags: frontmatter.tags || [],
                lastModified: stats.mtime,
                author: frontmatter.author,
                version: frontmatter.version,
            }

            return {
                path: relativePath,
                title,
                content,
                htmlContent,
                lastModified: stats.mtime,
                metadata,
                category,
                slug,
                breadcrumbs,
            }
        } catch (error) {
            logger.warn(
                `Could not parse enhanced markdown file ${filePath}:`,
                error
            )
            return null
        }
    }

    /**
     * Organize files into categories
     */
    private organizeIntoCategories(
        files: EnhancedDocumentationFile[]
    ): DocumentationCategory[] {
        const categoryMap = new Map<string, EnhancedDocumentationFile[]>()

        // Group files by category
        files.forEach((file) => {
            const category = file.category
            if (!categoryMap.has(category)) {
                categoryMap.set(category, [])
            }
            categoryMap.get(category)!.push(file)
        })

        // Convert to category objects and sort
        const categories: DocumentationCategory[] = []
        categoryMap.forEach((categoryFiles, categoryName) => {
            // Sort files within category by order, then by title
            categoryFiles.sort((a, b) => {
                const orderA = a.metadata.order || 999
                const orderB = b.metadata.order || 999
                if (orderA !== orderB) return orderA - orderB
                return a.title.localeCompare(b.title)
            })

            categories.push({
                name: this.formatCategoryName(categoryName),
                slug: this.generateSlug(categoryName),
                description: this.getCategoryDescription(categoryName),
                order: this.getCategoryOrder(categoryName),
                files: categoryFiles,
            })
        })

        // Sort categories by order
        categories.sort((a, b) => a.order - b.order)

        return categories
    }

    /**
     * Generate navigation structure
     */
    private generateNavigation(
        categories: DocumentationCategory[]
    ): NavigationItem[] {
        return categories.map((category) => ({
            title: category.name,
            path: `/docs/category/${category.slug}`,
            category: category.slug,
            order: category.order,
            children: category.files
                .map((file) => ({
                    title: file.title,
                    path: `/docs/content/${file.slug}`,
                    order: file.metadata.order || 999,
                }))
                .sort((a, b) => a.order - b.order),
        }))
    }

    /**
     * Discover all markdown files in the project
     */
    private async discoverMarkdownFiles(): Promise<string[]> {
        const markdownFiles: string[] = []
        const docsDir = path.join(this.projectRoot, 'docs')

        if (fs.existsSync(docsDir)) {
            await this.findMarkdownFilesRecursive(docsDir, markdownFiles)
        }

        return markdownFiles.filter(
            (file) =>
                !file.includes('node_modules') &&
                !file.includes('.git') &&
                !file.includes('dist') &&
                !file.includes('build')
        )
    }

    /**
     * Recursively find markdown files
     */
    private async findMarkdownFilesRecursive(
        dir: string,
        files: string[]
    ): Promise<void> {
        const entries = fs.readdirSync(dir, { withFileTypes: true })

        for (const entry of entries) {
            const fullPath = path.join(dir, entry.name)

            if (entry.isDirectory()) {
                await this.findMarkdownFilesRecursive(fullPath, files)
            } else if (entry.isFile() && entry.name.endsWith('.md')) {
                files.push(fullPath)
            }
        }
    }

    // Helper methods
    private extractTitle(content: string, filePath: string): string {
        const h1Match = content.match(/^#\s+(.+)$/m)
        if (h1Match) {
            return h1Match[1].trim()
        }

        const filename = path.basename(filePath, '.md')
        return (
            filename.charAt(0).toUpperCase() +
            filename.slice(1).replace(/[-_]/g, ' ')
        )
    }

    private generateSlug(text: string): string {
        return text
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '')
    }

    private inferCategory(relativePath: string): string {
        const pathParts = relativePath.split(path.sep)
        if (pathParts.length > 1 && pathParts[0] === 'docs') {
            return pathParts[1] || 'general'
        }
        return 'general'
    }

    private generateBreadcrumbs(
        relativePath: string,
        category: string
    ): string[] {
        const breadcrumbs = ['Documentation']
        if (category !== 'general') {
            breadcrumbs.push(this.formatCategoryName(category))
        }
        return breadcrumbs
    }

    private formatCategoryName(category: string): string {
        return category
            .split('-')
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ')
    }

    private getCategoryDescription(category: string): string {
        const descriptions: Record<string, string> = {
            api: 'REST API documentation and integration guides',
            development: 'Development setup, coding standards, and workflows',
            deployment: 'Production deployment and operations guides',
            architecture: 'System architecture and technical design',
            templates: 'Template system and customization guides',
            troubleshooting: 'Common issues and debugging guides',
            general: 'General documentation and overviews',
        }
        return descriptions[category] || 'Documentation files'
    }

    private getCategoryOrder(category: string): number {
        const orders: Record<string, number> = {
            general: 1,
            api: 2,
            development: 3,
            architecture: 4,
            templates: 5,
            deployment: 6,
            troubleshooting: 7,
        }
        return orders[category] || 999
    }

    private isCacheValid(): boolean {
        if (!this.cache) return false
        const now = new Date().getTime()
        const cacheTime = this.cache.lastUpdated.getTime()
        return now - cacheTime < this.cacheTimeout
    }

    public clearCache(): void {
        this.cache = null
        logger.info('Enhanced documentation cache cleared')
    }
}

export const enhancedDocumentationService =
    EnhancedDocumentationService.getInstance()
