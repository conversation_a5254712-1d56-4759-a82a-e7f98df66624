import { marked } from 'marked'
import * as fs from 'fs'
import * as path from 'path'
import Logger from '../utils/logger'

const logger = Logger.getInstance()

export interface DocumentationFile {
    path: string
    title: string
    content: string
    htmlContent: string
    lastModified: Date
    category?: string
    order?: number
    description?: string
    tags?: string[]
    metadata?: Record<string, any>
}

export interface DocumentationIndex {
    files: DocumentationFile[]
    lastUpdated: Date
}

export class DocumentationService {
    private static instance: DocumentationService | null = null
    private cache: DocumentationIndex | null = null
    private readonly projectRoot: string
    private readonly cacheTimeout = 5 * 60 * 1000 // 5 minutes

    private constructor() {
        this.projectRoot = path.resolve(__dirname, '../..')
        this.setupMarkedOptions()
    }

    public static getInstance(): DocumentationService {
        if (!this.instance) {
            this.instance = new DocumentationService()
        }
        return this.instance
    }

    private setupMarkedOptions(): void {
        marked.setOptions({
            gfm: true,
            breaks: false,
        })
    }

    /**
     * Get all documentation files with caching
     */
    public async getDocumentation(
        forceRefresh = false
    ): Promise<DocumentationIndex> {
        if (!forceRefresh && this.cache && this.isCacheValid()) {
            return this.cache
        }

        try {
            const files = await this.discoverMarkdownFiles()
            const documentationFiles = await Promise.all(
                files.map((filePath) => this.parseMarkdownFile(filePath))
            )

            this.cache = {
                files: documentationFiles.filter(
                    Boolean
                ) as DocumentationFile[],
                lastUpdated: new Date(),
            }

            logger.info(
                `Documentation cache updated with ${this.cache.files.length} files`
            )
            return this.cache
        } catch (error) {
            logger.error('Error loading documentation:', error)
            throw new Error('Failed to load documentation')
        }
    }

    /**
     * Get a specific documentation file by path
     */
    public async getDocumentationFile(
        relativePath: string
    ): Promise<DocumentationFile | null> {
        const documentation = await this.getDocumentation()
        return (
            documentation.files.find(
                (file) =>
                    file.path.includes(relativePath) ||
                    path.basename(file.path) === path.basename(relativePath)
            ) || null
        )
    }

    /**
     * Discover all markdown files in the project
     */
    private async discoverMarkdownFiles(): Promise<string[]> {
        const markdownFiles: string[] = []

        const searchDirectories = [
            path.join(this.projectRoot, 'docs'),
            path.join(this.projectRoot, 'src'),
            this.projectRoot,
        ]

        for (const dir of searchDirectories) {
            if (fs.existsSync(dir)) {
                await this.findMarkdownFilesRecursive(dir, markdownFiles)
            }
        }

        // Filter out node_modules and other unwanted directories
        return markdownFiles.filter(
            (file) =>
                !file.includes('node_modules') &&
                !file.includes('.git') &&
                !file.includes('dist') &&
                !file.includes('build')
        )
    }

    /**
     * Recursively find markdown files
     */
    private async findMarkdownFilesRecursive(
        dir: string,
        files: string[]
    ): Promise<void> {
        try {
            const entries = fs.readdirSync(dir, { withFileTypes: true })

            for (const entry of entries) {
                const fullPath = path.join(dir, entry.name)

                if (
                    entry.isDirectory() &&
                    !this.shouldSkipDirectory(entry.name)
                ) {
                    await this.findMarkdownFilesRecursive(fullPath, files)
                } else if (
                    entry.isFile() &&
                    entry.name.toLowerCase().endsWith('.md')
                ) {
                    files.push(fullPath)
                }
            }
        } catch (error) {
            logger.warn(`Could not read directory ${dir}:`, error)
        }
    }

    /**
     * Check if directory should be skipped
     */
    private shouldSkipDirectory(dirName: string): boolean {
        const skipDirs = [
            'node_modules',
            '.git',
            'dist',
            'build',
            '.next',
            'coverage',
        ]
        return skipDirs.includes(dirName) || dirName.startsWith('.')
    }

    /**
     * Parse a markdown file and convert to HTML
     */
    private async parseMarkdownFile(
        filePath: string
    ): Promise<DocumentationFile | null> {
        try {
            const content = fs.readFileSync(filePath, 'utf-8')
            const stats = fs.statSync(filePath)
            const relativePath = path.relative(this.projectRoot, filePath)

            // Extract title from first heading or filename
            const title = this.extractTitle(content, filePath)

            // Convert markdown to HTML
            const htmlContent = await marked(content)

            return {
                path: relativePath,
                title,
                content,
                htmlContent,
                lastModified: stats.mtime,
            }
        } catch (error) {
            logger.warn(`Could not parse markdown file ${filePath}:`, error)
            return null
        }
    }

    /**
     * Extract title from markdown content
     */
    private extractTitle(content: string, filePath: string): string {
        // Try to find first H1 heading
        const h1Match = content.match(/^#\s+(.+)$/m)
        if (h1Match) {
            return h1Match[1].trim()
        }

        // Fallback to filename without extension
        const filename = path.basename(filePath, '.md')
        return (
            filename.charAt(0).toUpperCase() +
            filename.slice(1).replace(/[-_]/g, ' ')
        )
    }

    /**
     * Check if cache is still valid
     */
    private isCacheValid(): boolean {
        if (!this.cache) return false
        const now = new Date().getTime()
        const cacheTime = this.cache.lastUpdated.getTime()
        return now - cacheTime < this.cacheTimeout
    }

    /**
     * Clear the cache
     */
    public clearCache(): void {
        this.cache = null
        logger.info('Documentation cache cleared')
    }
}
