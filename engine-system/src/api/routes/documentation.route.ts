import { Router } from 'express'
import {
    getDocumentationHub,
    refreshDocumentation,
    getDocumentationAPI,
    getDocumentationFile,
    getStaticDocumentation,
} from '../controllers/documentation-controller'

const documentationRouter = Router()

// Main documentation hub page (unified landing page)
documentationRouter.get('/', getDocumentationHub)

// API endpoint for JSON response
documentationRouter.get('/api', getDocumentationAPI)

// Refresh documentation cache
documentationRouter.get('/refresh', refreshDocumentation)

// View specific markdown documentation file
documentationRouter.get('/files/*', getDocumentationFile)

// Serve static HTML documentation guides
documentationRouter.get('/guides/*', getStaticDocumentation)

// Serve static documentation homepage
documentationRouter.get('/guides', getStaticDocumentation)

// Legacy redirects for backward compatibility
documentationRouter.get('/file/*', (req, res) => {
    const filePath = (req.params as any)[0] || ''
    res.redirect(301, `/docs/files/${filePath}`)
})

documentationRouter.get('/site/*', (req, res) => {
    const sitePath = (req.params as any)[0] || ''
    res.redirect(301, `/docs/guides/${sitePath}`)
})

documentationRouter.get('/site', (_req, res) => {
    res.redirect(301, '/docs/guides')
})

export { documentationRouter }
