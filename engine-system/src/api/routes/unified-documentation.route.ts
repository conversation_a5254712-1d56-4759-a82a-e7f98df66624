import { Router } from 'express'
import {
    getUnifiedDocumentationHub,
    getUnifiedDocumentationContent,
    getUnifiedDocumentationCategory,
    searchUnifiedDocumentation,
    getUnifiedDocumentationAPI,
    refreshUnifiedDocumentation,
} from '../controllers/unified-documentation-controller'
import { getStaticDocumentation } from '../controllers/documentation-controller'

const unifiedDocumentationRouter = Router()

// Main documentation hub page
unifiedDocumentationRouter.get('/', getUnifiedDocumentationHub)

// API endpoint for JSON response
unifiedDocumentationRouter.get('/api', getUnifiedDocumentationAPI)

// Refresh documentation cache
unifiedDocumentationRouter.get('/refresh', refreshUnifiedDocumentation)

// Search documentation
unifiedDocumentationRouter.get('/search', searchUnifiedDocumentation)

// View documentation by category
unifiedDocumentationRouter.get(
    '/category/:category',
    getUnifiedDocumentationCategory
)

// View specific documentation content by slug
unifiedDocumentationRouter.get('/content/:slug', getUnifiedDocumentationContent)

// Serve static assets (CSS, images, etc.)
unifiedDocumentationRouter.get('/assets/*', (req, res) => {
    const assetPath = (req.params as any)[0] || ''
    const fullPath = `assets/${assetPath}`

    // Delegate to the existing static documentation handler
    ;(req.params as any)[0] = fullPath
    getStaticDocumentation(req, res)
})

// Legacy redirects for backward compatibility
unifiedDocumentationRouter.get('/files/*', (req, res) => {
    const filePath = (req.params as any)[0] || ''
    const slug = filePath
        .replace(/\.md$/, '')
        .replace(/[^a-z0-9]+/gi, '-')
        .toLowerCase()
    res.redirect(301, `/docs/content/${slug}`)
})

unifiedDocumentationRouter.get('/guides/*', (req, res) => {
    const guidePath = (req.params as any)[0] || ''

    // Map common guide paths to new structure
    const pathMappings: Record<string, string> = {
        'overview/project-overview.html': '/docs/content/project-overview',
        'api/api-reference.html': '/docs/content/api',
        'development/development-guide.html': '/docs/content/development',
        'operations/deployment.html': '/docs/content/deployment',
        'troubleshooting/troubleshooting-guide.html':
            '/docs/content/troubleshooting',
        'api/integration-guide.html': '/docs/content/integration-guide',
        'index.html': '/docs',
        '': '/docs',
    }

    const redirectPath = pathMappings[guidePath] || '/docs'
    res.redirect(301, redirectPath)
})

unifiedDocumentationRouter.get('/site/*', (_req, res) => {
    res.redirect(301, '/docs')
})

unifiedDocumentationRouter.get('/site', (_req, res) => {
    res.redirect(301, '/docs')
})

unifiedDocumentationRouter.get('/file/*', (req, res) => {
    const filePath = (req.params as any)[0] || ''
    const slug = filePath
        .replace(/\.md$/, '')
        .replace(/[^a-z0-9]+/gi, '-')
        .toLowerCase()
    res.redirect(301, `/docs/content/${slug}`)
})

export { unifiedDocumentationRouter }
