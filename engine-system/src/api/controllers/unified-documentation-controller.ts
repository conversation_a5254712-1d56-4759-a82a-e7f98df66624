import { Request, Response } from 'express'
import { enhancedDocumentationService } from '../../services/enhanced-documentation-service'
import { createErrorResponse } from '../utils/error-response'
import Logger from '../../utils/logger'

const logger = Logger.getInstance()

/**
 * Serve the unified documentation hub page
 */
export const getUnifiedDocumentationHub = async (
    req: Request,
    res: Response
) => {
    try {
        logger.info('Serving unified documentation hub')
        const documentation =
            await enhancedDocumentationService.getDocumentation()
        const html = generateUnifiedHubHTML(documentation)

        res.setHeader('Content-Type', 'text/html')
        res.setHeader('Cache-Control', 'public, max-age=300') // 5 minutes
        res.status(200).send(html)

        logger.info(
            `Documentation hub served successfully with ${documentation.files.length} files`
        )
    } catch (error) {
        logger.error('Error serving unified documentation hub:', error)
        createErrorResponse(error, res)
    }
}

/**
 * Serve a specific documentation file by slug
 */
export const getUnifiedDocumentationContent = async (
    req: Request,
    res: Response
) => {
    try {
        const slug = req.params.slug
        if (!slug) {
            res.status(400).json({ error: 'Content slug is required' })
            return
        }

        const file =
            await enhancedDocumentationService.getDocumentationFile(slug)
        if (!file) {
            res.status(404).json({ error: 'Documentation content not found' })
            return
        }

        const documentation =
            await enhancedDocumentationService.getDocumentation()
        const html = generateUnifiedContentHTML(file, documentation)

        res.setHeader('Content-Type', 'text/html')
        res.status(200).send(html)
    } catch (error) {
        logger.error('Error serving unified documentation content:', error)
        createErrorResponse(error, res)
    }
}

/**
 * Serve documentation by category
 */
export const getUnifiedDocumentationCategory = async (
    req: Request,
    res: Response
) => {
    try {
        const categorySlug = req.params.category
        if (!categorySlug) {
            res.status(400).json({ error: 'Category slug is required' })
            return
        }

        const files =
            await enhancedDocumentationService.getFilesByCategory(categorySlug)
        if (files.length === 0) {
            res.status(404).json({ error: 'Category not found' })
            return
        }

        const documentation =
            await enhancedDocumentationService.getDocumentation()
        const category = documentation.categories.find(
            (cat) => cat.slug === categorySlug
        )
        const html = generateUnifiedCategoryHTML(
            category!,
            files,
            documentation
        )

        res.setHeader('Content-Type', 'text/html')
        res.status(200).send(html)
    } catch (error) {
        logger.error('Error serving unified documentation category:', error)
        createErrorResponse(error, res)
    }
}

/**
 * Search documentation content
 */
export const searchUnifiedDocumentation = async (
    req: Request,
    res: Response
) => {
    try {
        const query = req.query.q as string
        if (!query) {
            res.status(400).json({ error: 'Search query is required' })
            return
        }

        const results =
            await enhancedDocumentationService.searchDocumentation(query)
        const documentation =
            await enhancedDocumentationService.getDocumentation()
        const html = generateUnifiedSearchHTML(query, results, documentation)

        res.setHeader('Content-Type', 'text/html')
        res.status(200).send(html)
    } catch (error) {
        logger.error('Error searching unified documentation:', error)
        createErrorResponse(error, res)
    }
}

/**
 * Get documentation data as JSON API
 */
export const getUnifiedDocumentationAPI = async (
    req: Request,
    res: Response
) => {
    try {
        const documentation =
            await enhancedDocumentationService.getDocumentation()
        res.status(200).json({
            success: true,
            data: {
                categories: documentation.categories.map((cat) => ({
                    name: cat.name,
                    slug: cat.slug,
                    description: cat.description,
                    fileCount: cat.files.length,
                })),
                navigation: documentation.navigation,
                totalFiles: documentation.files.length,
                lastUpdated: documentation.lastUpdated,
            },
        })
    } catch (error) {
        logger.error('Error serving unified documentation API:', error)
        createErrorResponse(error, res)
    }
}

/**
 * Refresh documentation cache
 */
export const refreshUnifiedDocumentation = async (
    req: Request,
    res: Response
) => {
    try {
        enhancedDocumentationService.clearCache()
        const documentation =
            await enhancedDocumentationService.getDocumentation(true)

        res.status(200).json({
            success: true,
            message: 'Documentation cache refreshed',
            data: {
                filesCount: documentation.files.length,
                categoriesCount: documentation.categories.length,
                lastUpdated: documentation.lastUpdated,
            },
        })
    } catch (error) {
        logger.error('Error refreshing unified documentation:', error)
        createErrorResponse(error, res)
    }
}

/**
 * Generate HTML for the unified documentation hub
 */
function generateUnifiedHubHTML(documentation: any): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Notification Engine Documentation - Comprehensive guides, API reference, and technical documentation">
    <title>Documentation Hub - Notification Engine</title>
    <link rel="stylesheet" href="/docs/assets/unified-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>

    ${generateHeader()}

    <main id="main-content" class="main-content">
        <div class="container">
            <div class="hero-section">
                <h1 class="hero-title">Documentation Hub</h1>
                <p class="hero-description">
                    Comprehensive documentation for the Notification Engine -
                    your guide to multi-channel notifications, API integration, and system architecture.
                </p>

                <div class="search-container">
                    <form action="/docs/search" method="GET" class="search-form">
                        <input type="text" name="q" placeholder="Search documentation..." class="search-input" required>
                        <button type="submit" class="search-button">Search</button>
                    </form>
                </div>
            </div>

            <div class="categories-grid">
                ${documentation.categories
                    .map(
                        (category: any) => `
                    <div class="category-card">
                        <div class="category-header">
                            <h2 class="category-title">
                                <a href="/docs/category/${category.slug}">${category.name}</a>
                            </h2>
                            <span class="category-count">${category.files.length} files</span>
                        </div>
                        <p class="category-description">${category.description}</p>
                        <div class="category-files">
                            ${category.files
                                .slice(0, 3)
                                .map(
                                    (file: any) => `
                                <a href="/docs/content/${file.slug}" class="file-link">
                                    ${file.title}
                                </a>
                            `
                                )
                                .join('')}
                            ${
                                category.files.length > 3
                                    ? `
                                <a href="/docs/category/${category.slug}" class="view-all-link">
                                    View all ${category.files.length} files →
                                </a>
                            `
                                    : ''
                            }
                        </div>
                    </div>
                `
                    )
                    .join('')}
            </div>

            <div class="quick-stats">
                <div class="stat-item">
                    <span class="stat-number">${documentation.files.length}</span>
                    <span class="stat-label">Documentation Files</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${documentation.categories.length}</span>
                    <span class="stat-label">Categories</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${documentation.lastUpdated.toLocaleDateString()}</span>
                    <span class="stat-label">Last Updated</span>
                </div>
            </div>
        </div>
    </main>

    ${generateFooter()}
</body>
</html>`
}

/**
 * Generate HTML for unified content pages
 */
function generateUnifiedContentHTML(file: any, documentation: any): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="${file.metadata.description || file.title} - Notification Engine Documentation">
    <title>${file.title} - Notification Engine Docs</title>
    <link rel="stylesheet" href="/docs/assets/unified-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>

    ${generateHeader()}

    <main id="main-content" class="main-content">
        <div class="container">
            <div class="content-layout">
                <aside class="sidebar">
                    ${generateSidebar(documentation.navigation, file.category)}
                </aside>

                <article class="content-main">
                    <div class="content-header">
                        <nav class="breadcrumb">
                            ${file.breadcrumbs
                                .map((crumb: string, index: number) =>
                                    index === file.breadcrumbs.length - 1
                                        ? `<span class="breadcrumb-current">${crumb}</span>`
                                        : `<a href="/docs" class="breadcrumb-link">${crumb}</a>`
                                )
                                .join(' / ')}
                        </nav>

                        <h1 class="content-title">${file.title}</h1>

                        ${
                            file.metadata.description
                                ? `
                            <p class="content-description">${file.metadata.description}</p>
                        `
                                : ''
                        }

                        <div class="content-meta">
                            <span class="meta-item">Category: ${file.category}</span>
                            <span class="meta-item">Updated: ${file.lastModified.toLocaleDateString()}</span>
                            ${
                                file.metadata.tags &&
                                file.metadata.tags.length > 0
                                    ? `
                                <span class="meta-item">
                                    Tags: ${file.metadata.tags.join(', ')}
                                </span>
                            `
                                    : ''
                            }
                        </div>
                    </div>

                    <div class="content-body">
                        ${file.htmlContent}
                    </div>
                </article>
            </div>
        </div>
    </main>

    ${generateFooter()}
</body>
</html>`
}

/**
 * Generate category page HTML
 */
function generateUnifiedCategoryHTML(
    category: any,
    files: any[],
    documentation: any
): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="${category.description} - Notification Engine Documentation">
    <title>${category.name} - Notification Engine Docs</title>
    <link rel="stylesheet" href="/docs/assets/unified-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>

    ${generateHeader()}

    <main id="main-content" class="main-content">
        <div class="container">
            <div class="category-header">
                <nav class="breadcrumb">
                    <a href="/docs" class="breadcrumb-link">Documentation</a> /
                    <span class="breadcrumb-current">${category.name}</span>
                </nav>

                <h1 class="category-title">${category.name}</h1>
                <p class="category-description">${category.description}</p>
            </div>

            <div class="files-grid">
                ${files
                    .map(
                        (file) => `
                    <div class="file-card">
                        <h3 class="file-title">
                            <a href="/docs/content/${file.slug}">${file.title}</a>
                        </h3>
                        ${
                            file.metadata.description
                                ? `
                            <p class="file-description">${file.metadata.description}</p>
                        `
                                : ''
                        }
                        <div class="file-meta">
                            <span class="file-updated">Updated: ${file.lastModified.toLocaleDateString()}</span>
                            ${
                                file.metadata.tags &&
                                file.metadata.tags.length > 0
                                    ? `
                                <div class="file-tags">
                                    ${file.metadata.tags
                                        .map(
                                            (tag: string) => `
                                        <span class="tag">${tag}</span>
                                    `
                                        )
                                        .join('')}
                                </div>
                            `
                                    : ''
                            }
                        </div>
                    </div>
                `
                    )
                    .join('')}
            </div>
        </div>
    </main>

    ${generateFooter()}
</body>
</html>`
}

/**
 * Generate search results HTML
 */
function generateUnifiedSearchHTML(
    query: string,
    results: any[],
    documentation: any
): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Search results for '${query}' - Notification Engine Documentation">
    <title>Search: ${query} - Notification Engine Docs</title>
    <link rel="stylesheet" href="/docs/assets/unified-styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>

    ${generateHeader()}

    <main id="main-content" class="main-content">
        <div class="container">
            <div class="search-header">
                <h1 class="search-title">Search Results</h1>
                <p class="search-info">Found ${results.length} results for "${query}"</p>

                <div class="search-container">
                    <form action="/docs/search" method="GET" class="search-form">
                        <input type="text" name="q" value="${query}" placeholder="Search documentation..." class="search-input" required>
                        <button type="submit" class="search-button">Search</button>
                    </form>
                </div>
            </div>

            ${
                results.length > 0
                    ? `
                <div class="search-results">
                    ${results
                        .map(
                            (file) => `
                        <div class="search-result">
                            <h3 class="result-title">
                                <a href="/docs/content/${file.slug}">${file.title}</a>
                            </h3>
                            <p class="result-category">Category: ${file.category}</p>
                            ${
                                file.metadata.description
                                    ? `
                                <p class="result-description">${file.metadata.description}</p>
                            `
                                    : ''
                            }
                            <div class="result-meta">
                                <span class="result-updated">Updated: ${file.lastModified.toLocaleDateString()}</span>
                                ${
                                    file.metadata.tags &&
                                    file.metadata.tags.length > 0
                                        ? `
                                    <div class="result-tags">
                                        ${file.metadata.tags
                                            .map(
                                                (tag: string) => `
                                            <span class="tag">${tag}</span>
                                        `
                                            )
                                            .join('')}
                                    </div>
                                `
                                        : ''
                                }
                            </div>
                        </div>
                    `
                        )
                        .join('')}
                </div>
            `
                    : `
                <div class="no-results">
                    <h2>No results found</h2>
                    <p>Try adjusting your search terms or browse our categories:</p>
                    <div class="category-links">
                        ${documentation.categories
                            .map(
                                (cat: any) => `
                            <a href="/docs/category/${cat.slug}" class="category-link">${cat.name}</a>
                        `
                            )
                            .join('')}
                    </div>
                </div>
            `
            }
        </div>
    </main>

    ${generateFooter()}
</body>
</html>`
}

/**
 * Generate common header
 */
function generateHeader(): string {
    return `
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="/docs" class="logo-link">
                        <span class="logo-icon">🔔</span>
                        <span class="logo-text">Notification Engine</span>
                    </a>
                </div>

                <nav class="main-nav">
                    <a href="/docs" class="nav-link">Documentation</a>
                    <a href="/docs/category/api" class="nav-link">API</a>
                    <a href="/docs/category/development" class="nav-link">Development</a>
                    <a href="/docs/search" class="nav-link">Search</a>
                </nav>
            </div>
        </div>
    </header>`
}

/**
 * Generate sidebar navigation
 */
function generateSidebar(navigation: any[], currentCategory?: string): string {
    return `
    <nav class="sidebar-nav">
        <h3 class="sidebar-title">Documentation</h3>
        ${navigation
            .map(
                (item) => `
            <div class="nav-section ${item.category === currentCategory ? 'active' : ''}">
                <h4 class="nav-section-title">
                    <a href="${item.path}">${item.title}</a>
                </h4>
                ${
                    item.children && item.children.length > 0
                        ? `
                    <ul class="nav-section-items">
                        ${item.children
                            .map(
                                (child: any) => `
                            <li><a href="${child.path}" class="nav-item-link">${child.title}</a></li>
                        `
                            )
                            .join('')}
                    </ul>
                `
                        : ''
                }
            </div>
        `
            )
            .join('')}
    </nav>`
}

/**
 * Generate common footer
 */
function generateFooter(): string {
    return `
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2025 Notification Engine Documentation</p>
                <div class="footer-links">
                    <a href="/docs/api">API Reference</a>
                    <a href="/docs/refresh">Refresh Cache</a>
                </div>
            </div>
        </div>
    </footer>`
}
