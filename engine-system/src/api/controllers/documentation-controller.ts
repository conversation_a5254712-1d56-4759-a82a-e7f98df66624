import { Request, Response } from 'express'
import { DocumentationService } from '../../services/documentation-service'
import { createErrorResponse } from '../utils/custom.errors'
import Logger from '../../utils/logger'
import path from 'path'
import fs from 'fs/promises'

const logger = Logger.getInstance()
const documentationService = DocumentationService.getInstance()

/**
 * Serve the main documentation hub page (unified landing page)
 */
export const getDocumentationHub = (_req: Request, res: Response) => {
    documentationService
        .getDocumentation()
        .then((documentation) => {
            // Group files by directory for better organization
            const groupedFiles = groupFilesByDirectory(documentation.files)

            const html = generateHubHTML(
                groupedFiles,
                documentation.lastUpdated
            )

            res.setHeader('Content-Type', 'text/html')
            res.status(200).send(html)
        })
        .catch((error) => {
            logger.error('Error serving documentation hub:', error)
            createErrorResponse(error, res)
        })
}

/**
 * Legacy function for backward compatibility
 */
export const getDocumentationIndex = getDocumentationHub

/**
 * Serve a specific documentation file
 */
export const getDocumentationFile = (req: Request, res: Response) => {
    // Extract file path from wildcard route
    const filePath =
        req.params[0] ||
        req.path.replace('/docs/files/', '').replace('/docs/file/', '')

    if (!filePath) {
        res.status(400).json({ error: 'File path is required' })
        return
    }

    documentationService
        .getDocumentationFile(decodeURIComponent(filePath))
        .then((file) => {
            if (!file) {
                res.status(404).json({ error: 'Documentation file not found' })
                return
            }

            const html = generateFileHTML(file)

            res.setHeader('Content-Type', 'text/html')
            res.status(200).send(html)
        })
        .catch((error) => {
            logger.error('Error serving documentation file:', error)
            createErrorResponse(error, res)
        })
}

/**
 * Refresh documentation cache
 */
export const refreshDocumentation = (_req: Request, res: Response) => {
    documentationService.clearCache()
    documentationService
        .getDocumentation(true)
        .then((documentation) => {
            res.status(200).json({
                message: 'Documentation cache refreshed',
                filesCount: documentation.files.length,
                lastUpdated: documentation.lastUpdated,
            })
        })
        .catch((error) => {
            logger.error('Error refreshing documentation:', error)
            createErrorResponse(error, res)
        })
}

/**
 * Get documentation as JSON API
 */
export const getDocumentationAPI = (_req: Request, res: Response) => {
    documentationService
        .getDocumentation()
        .then((documentation) => {
            res.status(200).json(documentation)
        })
        .catch((error) => {
            logger.error('Error serving documentation API:', error)
            createErrorResponse(error, res)
        })
}

/**
 * Serve the static HTML documentation site
 */
export const getStaticDocumentation = (req: Request, res: Response) => {
    const handleStaticFile = async () => {
        try {
            // Get the requested path, default to index.html
            const requestedPath = req.params[0] || 'index.html'

            // Construct the full path to the docs-site directory
            const docsPath = path.join(
                process.cwd(),
                'docs-site',
                requestedPath
            )

            // Security check: ensure the path is within the docs-site directory
            const normalizedPath = path.normalize(docsPath)
            const docsDir = path.join(process.cwd(), 'docs-site')

            if (!normalizedPath.startsWith(docsDir)) {
                res.status(403).json({ error: 'Access denied' })
                return
            }

            // Check if file exists
            try {
                const stats = await fs.stat(normalizedPath)

                if (stats.isDirectory()) {
                    // If it's a directory, try to serve index.html from that directory
                    const indexPath = path.join(normalizedPath, 'index.html')
                    try {
                        await fs.access(indexPath)
                        const content = await fs.readFile(indexPath, 'utf-8')
                        res.setHeader(
                            'Content-Type',
                            'text/html; charset=utf-8'
                        )
                        res.send(content)
                        return
                    } catch {
                        res.status(404).json({
                            error: 'Directory index not found',
                        })
                        return
                    }
                }

                // Serve the file
                const content = await fs.readFile(normalizedPath, 'utf-8')

                // Set appropriate content type based on file extension
                const ext = path.extname(normalizedPath).toLowerCase()
                const contentTypes: { [key: string]: string } = {
                    '.html': 'text/html; charset=utf-8',
                    '.css': 'text/css; charset=utf-8',
                    '.js': 'application/javascript; charset=utf-8',
                    '.json': 'application/json; charset=utf-8',
                    '.png': 'image/png',
                    '.jpg': 'image/jpeg',
                    '.jpeg': 'image/jpeg',
                    '.gif': 'image/gif',
                    '.svg': 'image/svg+xml',
                    '.ico': 'image/x-icon',
                }

                const contentType =
                    contentTypes[ext] || 'text/plain; charset=utf-8'
                res.setHeader('Content-Type', contentType)

                // Set cache headers for static assets
                if (
                    [
                        '.css',
                        '.js',
                        '.png',
                        '.jpg',
                        '.jpeg',
                        '.gif',
                        '.svg',
                        '.ico',
                    ].includes(ext)
                ) {
                    res.setHeader('Cache-Control', 'public, max-age=86400') // 24 hours
                }

                res.send(content)
            } catch (error) {
                if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
                    res.status(404).json({
                        error: 'Documentation page not found',
                    })
                    return
                }
                throw error
            }
        } catch (error) {
            logger.error('Error serving static documentation:', error)
            createErrorResponse(error, res)
        }
    }

    // Call the async function
    handleStaticFile().catch((error) => {
        logger.error('Error in getStaticDocumentation:', error)
        createErrorResponse(error, res)
    })
}

/**
 * Group files by their directory structure
 */
function groupFilesByDirectory(files: any[]) {
    const groups: { [key: string]: any[] } = {}

    files.forEach((file) => {
        const dir = file.path.includes('/') ? file.path.split('/')[0] : 'Root'
        if (!groups[dir]) {
            groups[dir] = []
        }
        groups[dir].push(file)
    })

    // Sort files within each group
    Object.keys(groups).forEach((key) => {
        groups[key].sort((a, b) => a.title.localeCompare(b.title))
    })

    return groups
}

/**
 * Generate HTML for the unified documentation hub page
 */
function generateHubHTML(
    groupedFiles: { [key: string]: any[] },
    lastUpdated: Date
): string {
    const groups = Object.keys(groupedFiles).sort()
    const totalFiles = Object.values(groupedFiles).flat().length

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Notification Engine Documentation Hub - Comprehensive documentation for the notification engine system">
    <title>Documentation Hub - Notification Engine</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    ${getCommonStyles()}
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Header Navigation -->
    <header class="header" role="banner">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1 class="logo-text">
                        <span class="logo-icon">🔔</span>
                        Notification Engine
                    </h1>
                    <p class="logo-subtitle">Documentation Hub</p>
                </div>

                <nav class="main-nav" role="navigation" aria-label="Main navigation">
                    <ul class="nav-list">
                        <li><a href="/docs/guides" class="nav-link">User Guides</a></li>
                        <li><a href="#markdown-docs" class="nav-link">Markdown Docs</a></li>
                        <li><a href="/docs/api" class="nav-link">JSON API</a></li>
                    </ul>
                </nav>

                <div class="header-actions">
                    <a href="/docs/guides" class="btn btn-primary">View Guides</a>
                    <a href="/docs/refresh" class="btn btn-secondary">Refresh</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <div class="container">
            <!-- Hero Section -->
            <section class="hero-section">
                <div class="hero-content">
                    <h1 class="hero-title">📚 Documentation Hub</h1>
                    <p class="hero-description">
                        Welcome to the Notification Engine documentation. Choose from comprehensive user guides
                        or browse the markdown documentation files for detailed technical information.
                    </p>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <span class="stat-number">${totalFiles}</span>
                            <span class="stat-label">Documentation Files</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${groups.length}</span>
                            <span class="stat-label">Categories</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">2</span>
                            <span class="stat-label">Documentation Types</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Documentation Types -->
            <section class="documentation-types">
                <div class="type-grid">
                    <!-- User Guides -->
                    <div class="doc-type-card primary">
                        <div class="doc-type-header">
                            <div class="doc-type-icon">📖</div>
                            <h2 class="doc-type-title">User Guides</h2>
                            <p class="doc-type-subtitle">Comprehensive Documentation</p>
                        </div>
                        <div class="doc-type-content">
                            <p class="doc-type-description">
                                Complete user guides with step-by-step instructions, examples, and best practices.
                                Perfect for getting started and learning the system.
                            </p>
                            <ul class="doc-type-features">
                                <li>🚀 Getting Started Guide</li>
                                <li>🔌 API Integration Examples</li>
                                <li>🏗️ Architecture Overview</li>
                                <li>⚙️ Deployment Instructions</li>
                                <li>👨‍💻 Development Setup</li>
                            </ul>
                        </div>
                        <div class="doc-type-actions">
                            <a href="/docs/guides" class="btn btn-primary btn-large">
                                Browse User Guides →
                            </a>
                        </div>
                    </div>

                    <!-- Markdown Documentation -->
                    <div class="doc-type-card secondary">
                        <div class="doc-type-header">
                            <div class="doc-type-icon">📄</div>
                            <h2 class="doc-type-title">Markdown Docs</h2>
                            <p class="doc-type-subtitle">Technical Documentation</p>
                        </div>
                        <div class="doc-type-content">
                            <p class="doc-type-description">
                                Raw markdown documentation files with technical details, code examples,
                                and in-depth explanations. Updated: ${lastUpdated.toLocaleDateString()}
                            </p>
                            <div class="doc-categories">
                                ${groups
                                    .slice(0, 4)
                                    .map(
                                        (group) => `
                                    <div class="category-item">
                                        <span class="category-icon">${group === 'Root' ? '📄' : '📁'}</span>
                                        <span class="category-name">${group === 'Root' ? 'Root Files' : group}</span>
                                        <span class="category-count">${groupedFiles[group].length}</span>
                                    </div>
                                `
                                    )
                                    .join('')}
                                ${groups.length > 4 ? `<div class="category-item more">+${groups.length - 4} more categories</div>` : ''}
                            </div>
                        </div>
                        <div class="doc-type-actions">
                            <a href="#markdown-docs" class="btn btn-secondary btn-large">
                                Browse Markdown Docs →
                            </a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions-section">
                <h2 class="section-title">Quick Actions</h2>
                <div class="quick-actions-grid">
                    <a href="/docs/guides/overview/project-overview.html" class="quick-action-card">
                        <div class="action-icon">🎯</div>
                        <h3 class="action-title">Project Overview</h3>
                        <p class="action-description">Get a high-level understanding of the system</p>
                    </a>
                    <a href="/docs/guides/api/api-reference.html" class="quick-action-card">
                        <div class="action-icon">🔌</div>
                        <h3 class="action-title">API Reference</h3>
                        <p class="action-description">Complete API documentation and examples</p>
                    </a>
                    <a href="/docs/guides/development/development-guide.html" class="quick-action-card">
                        <div class="action-icon">👨‍💻</div>
                        <h3 class="action-title">Development Setup</h3>
                        <p class="action-description">Set up your development environment</p>
                    </a>
                    <a href="/docs/api" class="quick-action-card">
                        <div class="action-icon">📋</div>
                        <h3 class="action-title">JSON API</h3>
                        <p class="action-description">Access documentation data programmatically</p>
                    </a>
                </div>
            </section>
        </div>
    </main>

    <!-- Markdown Documentation Section -->
    <section id="markdown-docs" class="markdown-docs-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">📄 Markdown Documentation</h2>
                <p class="section-description">
                    Browse technical documentation files organized by category
                </p>
            </div>

            ${groups
                .map(
                    (group) => `
                <div class="doc-group">
                    <div class="group-header">
                        <h3 class="group-title">
                            ${group === 'Root' ? '📄 Root Documentation' : `📁 ${group}`}
                        </h3>
                        <span class="group-count">${groupedFiles[group].length} files</span>
                    </div>
                    <div class="file-grid">
                        ${groupedFiles[group]
                            .map(
                                (file) => `
                            <article class="file-card">
                                <div class="file-card-header">
                                    <h4 class="file-title">
                                        <a href="/docs/files/${encodeURIComponent(file.path)}" class="file-link">
                                            ${file.title}
                                        </a>
                                    </h4>
                                    <span class="file-type">MD</span>
                                </div>
                                <div class="file-meta">
                                    <span class="file-path">
                                        <span class="path-icon">📄</span>
                                        ${file.path}
                                    </span>
                                    <span class="file-modified">
                                        <span class="modified-icon">🕒</span>
                                        ${file.lastModified.toLocaleDateString()}
                                    </span>
                                </div>
                                <div class="file-preview">
                                    ${file.htmlContent.substring(0, 150).replace(/<[^>]*>/g, '')}...
                                </div>
                                <div class="file-actions">
                                    <a href="/docs/files/${encodeURIComponent(file.path)}" class="view-btn">
                                        <span class="btn-icon">📖</span>
                                        <span class="btn-text">View</span>
                                    </a>
                                </div>
                            </article>
                        `
                            )
                            .join('')}
                    </div>
                </div>
            `
                )
                .join('')}
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 class="footer-title">Documentation</h4>
                    <ul class="footer-links">
                        <li><a href="/docs">Documentation Hub</a></li>
                        <li><a href="/docs/guides">User Guides</a></li>
                        <li><a href="/docs/api">JSON API</a></li>
                        <li><a href="/docs/refresh">Refresh Cache</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="/docs/guides/overview/project-overview.html">Project Overview</a></li>
                        <li><a href="/docs/guides/api/api-reference.html">API Reference</a></li>
                        <li><a href="/docs/guides/development/development-guide.html">Development Guide</a></li>
                        <li><a href="/docs/guides/operations/deployment.html">Deployment</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p class="footer-copyright">
                    © 2025 Notification Engine Documentation. Generated by Documentation Service.
                </p>
            </div>
        </div>
    </footer>
</body>
</html>`
}

/**
 * Legacy function - Generate HTML for the documentation index page
 */
function generateIndexHTML(
    groupedFiles: { [key: string]: any[] },
    lastUpdated: Date
): string {
    const groups = Object.keys(groupedFiles).sort()

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Notification Engine Documentation - Comprehensive documentation for the notification engine system">
    <title>Notification Engine Documentation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    ${getCommonStyles()}
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Header Navigation -->
    <header class="header" role="banner">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1 class="logo-text">
                        <span class="logo-icon">🔔</span>
                        Notification Engine
                    </h1>
                    <p class="logo-subtitle">Documentation Hub</p>
                </div>

                <nav class="main-nav" role="navigation" aria-label="Main navigation">
                    <ul class="nav-list">
                        <li><a href="/docs/site" class="nav-link">Static Docs</a></li>
                        <li><a href="/docs/api" class="nav-link">JSON API</a></li>
                        <li><a href="/docs/refresh" class="nav-link">Refresh</a></li>
                    </ul>
                </nav>

                <div class="header-actions">
                    <a href="/docs/site" class="btn btn-primary">View Static Site</a>
                    <a href="/docs/api" class="btn btn-secondary">JSON API</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">📚 Documentation Index</h1>
                <p class="page-description">
                    Browse all documentation files organized by category. This index shows markdown files
                    from the docs directory with live content preview.
                </p>
                <div class="meta-info">
                    <span class="meta-item">
                        <span class="meta-icon">🕒</span>
                        Last updated: ${lastUpdated.toLocaleString()}
                    </span>
                    <span class="meta-item">
                        <span class="meta-icon">📄</span>
                        ${Object.values(groupedFiles).flat().length} files
                    </span>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="/docs/refresh" class="action-btn refresh-btn">
                    <span class="action-icon">🔄</span>
                    <span class="action-text">Refresh Cache</span>
                </a>
                <a href="/docs/api" class="action-btn api-btn">
                    <span class="action-icon">📋</span>
                    <span class="action-text">JSON API</span>
                </a>
                <a href="/docs/site" class="action-btn site-btn">
                    <span class="action-icon">🌐</span>
                    <span class="action-text">Static Site</span>
                </a>
            </div>

            <!-- Documentation Groups -->
            <div class="documentation-content">
                ${groups
                    .map(
                        (group) => `
                    <section class="doc-group" aria-labelledby="group-${group.toLowerCase().replace(/\s+/g, '-')}">
                        <div class="group-header">
                            <h2 id="group-${group.toLowerCase().replace(/\s+/g, '-')}" class="group-title">
                                ${group === 'Root' ? '📄 Root Documentation' : `📁 ${group}`}
                            </h2>
                            <span class="group-count">${groupedFiles[group].length} files</span>
                        </div>
                        <div class="file-grid">
                            ${groupedFiles[group]
                                .map(
                                    (file) => `
                                <article class="file-card">
                                    <div class="file-card-header">
                                        <h3 class="file-title">
                                            <a href="/docs/file/${encodeURIComponent(file.path)}" class="file-link">
                                                ${file.title}
                                            </a>
                                        </h3>
                                        <span class="file-type">Markdown</span>
                                    </div>
                                    <div class="file-meta">
                                        <span class="file-path">
                                            <span class="path-icon">📄</span>
                                            ${file.path}
                                        </span>
                                        <span class="file-modified">
                                            <span class="modified-icon">🕒</span>
                                            ${file.lastModified.toLocaleDateString()}
                                        </span>
                                    </div>
                                    <div class="file-preview">
                                        ${file.htmlContent.substring(0, 200).replace(/<[^>]*>/g, '')}...
                                    </div>
                                    <div class="file-actions">
                                        <a href="/docs/file/${encodeURIComponent(file.path)}" class="view-btn">
                                            <span class="btn-icon">📖</span>
                                            <span class="btn-text">View Document</span>
                                        </a>
                                    </div>
                                </article>
                            `
                                )
                                .join('')}
                        </div>
                    </section>
                `
                    )
                    .join('')}
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 class="footer-title">Documentation</h4>
                    <ul class="footer-links">
                        <li><a href="/docs">Documentation Index</a></li>
                        <li><a href="/docs/site">Static Documentation</a></li>
                        <li><a href="/docs/api">JSON API</a></li>
                        <li><a href="/docs/refresh">Refresh Cache</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="/docs/site/overview/project-overview.html">Project Overview</a></li>
                        <li><a href="/docs/site/api/api-reference.html">API Reference</a></li>
                        <li><a href="/docs/site/development/development-guide.html">Development Guide</a></li>
                        <li><a href="/docs/site/operations/deployment.html">Deployment</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p class="footer-copyright">
                    © 2025 Notification Engine Documentation. Generated by Documentation Service.
                </p>
            </div>
        </div>
    </footer>
</body>
</html>`
}

/**
 * Generate HTML for a specific documentation file
 */
function generateFileHTML(file: any): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="${file.title} - Notification Engine Documentation">
    <title>${file.title} - Notification Engine Docs</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    ${getCommonStyles()}
    ${getMarkdownStyles()}
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Header Navigation -->
    <header class="header" role="banner">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="/docs" class="logo-link">
                        <h1 class="logo-text">
                            <span class="logo-icon">🔔</span>
                            Notification Engine
                        </h1>
                        <p class="logo-subtitle">Documentation Hub</p>
                    </a>
                </div>

                <nav class="main-nav" role="navigation" aria-label="Main navigation">
                    <ul class="nav-list">
                        <li><a href="/docs" class="nav-link">Documentation Hub</a></li>
                        <li><a href="/docs/guides" class="nav-link">User Guides</a></li>
                        <li><a href="/docs/api" class="nav-link">JSON API</a></li>
                    </ul>
                </nav>

                <div class="header-actions">
                    <a href="/docs" class="btn btn-secondary">← Back to Hub</a>
                    <a href="/docs/guides" class="btn btn-primary">User Guides</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="main-content" role="main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <nav class="breadcrumb" aria-label="Breadcrumb">
                    <ol class="breadcrumb-list">
                        <li><a href="/docs">📚 Documentation Hub</a></li>
                        <li><a href="/docs#markdown-docs">Markdown Docs</a></li>
                        <li aria-current="page">${file.title}</li>
                    </ol>
                </nav>

                <h1 class="page-title">${file.title}</h1>
                <div class="file-meta-info">
                    <span class="meta-item">
                        <span class="meta-icon">📄</span>
                        <span class="meta-text">${file.path}</span>
                    </span>
                    <span class="meta-item">
                        <span class="meta-icon">🕒</span>
                        <span class="meta-text">Modified: ${file.lastModified.toLocaleString()}</span>
                    </span>
                </div>
            </div>

            <!-- Document Content -->
            <div class="document-content">
                <article class="markdown-content" role="article">
                    ${file.htmlContent}
                </article>
            </div>

            <!-- Page Navigation -->
            <nav class="page-nav" aria-label="Page navigation">
                <a href="/docs" class="page-nav-link page-nav-prev">
                    ← Back to Documentation Hub
                </a>
                <a href="/docs/guides" class="page-nav-link page-nav-next">
                    View User Guides →
                </a>
            </nav>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 class="footer-title">Documentation</h4>
                    <ul class="footer-links">
                        <li><a href="/docs">Documentation Hub</a></li>
                        <li><a href="/docs/guides">User Guides</a></li>
                        <li><a href="/docs/api">JSON API</a></li>
                        <li><a href="/docs/refresh">Refresh Cache</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="/docs/guides/overview/project-overview.html">Project Overview</a></li>
                        <li><a href="/docs/guides/api/api-reference.html">API Reference</a></li>
                        <li><a href="/docs/guides/development/development-guide.html">Development Guide</a></li>
                        <li><a href="/docs/guides/operations/deployment.html">Deployment</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p class="footer-copyright">
                    © 2025 Notification Engine Documentation. Generated by Documentation Service.
                </p>
            </div>
        </div>
    </footer>
</body>
</html>`
}

/**
 * Common CSS styles
 */
function getCommonStyles(): string {
    return `
<style>
    /* CSS Reset and Base Styles */
    *, *::before, *::after {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    :root {
        /* Enhanced Color Palette */
        --primary-color: #1a202c;
        --primary-light: #2d3748;
        --secondary-color: #3182ce;
        --secondary-light: #4299e1;
        --accent-color: #38a169;
        --accent-light: #48bb78;
        --danger-color: #e53e3e;
        --warning-color: #dd6b20;
        --success-color: #38a169;
        --info-color: #3182ce;

        /* Sophisticated Neutral Colors */
        --text-primary: #1a202c;
        --text-secondary: #4a5568;
        --text-muted: #718096;
        --text-light: #a0aec0;
        --border-light: #e2e8f0;
        --border-medium: #cbd5e0;
        --border-dark: #a0aec0;
        --background-primary: #ffffff;
        --background-secondary: #f7fafc;
        --background-tertiary: #edf2f7;
        --background-accent: #ebf8ff;

        /* Refined Spacing Scale */
        --space-0: 0;
        --space-px: 1px;
        --space-0_5: 0.125rem;  /* 2px */
        --space-1: 0.25rem;     /* 4px */
        --space-1_5: 0.375rem;  /* 6px */
        --space-2: 0.5rem;      /* 8px */
        --space-2_5: 0.625rem;  /* 10px */
        --space-3: 0.75rem;     /* 12px */
        --space-3_5: 0.875rem;  /* 14px */
        --space-4: 1rem;        /* 16px */
        --space-5: 1.25rem;     /* 20px */
        --space-6: 1.5rem;      /* 24px */
        --space-7: 1.75rem;     /* 28px */
        --space-8: 2rem;        /* 32px */
        --space-10: 2.5rem;     /* 40px */
        --space-12: 3rem;       /* 48px */
        --space-16: 4rem;       /* 64px */
        --space-20: 5rem;       /* 80px */
        --space-24: 6rem;       /* 96px */

        /* Professional Typography Scale */
        --font-size-xs: 0.75rem;     /* 12px */
        --font-size-sm: 0.875rem;    /* 14px */
        --font-size-base: 1rem;      /* 16px */
        --font-size-lg: 1.125rem;    /* 18px */
        --font-size-xl: 1.25rem;     /* 20px */
        --font-size-2xl: 1.5rem;     /* 24px */
        --font-size-3xl: 1.875rem;   /* 30px */
        --font-size-4xl: 2.25rem;    /* 36px */
        --font-size-5xl: 3rem;       /* 48px */
        --font-size-6xl: 3.75rem;    /* 60px */

        /* Line Heights */
        --leading-none: 1;
        --leading-tight: 1.25;
        --leading-snug: 1.375;
        --leading-normal: 1.5;
        --leading-relaxed: 1.625;
        --leading-loose: 2;

        /* Enhanced Border Radius */
        --radius-none: 0;
        --radius-sm: 0.125rem;    /* 2px */
        --radius-base: 0.25rem;   /* 4px */
        --radius-md: 0.375rem;    /* 6px */
        --radius-lg: 0.5rem;      /* 8px */
        --radius-xl: 0.75rem;     /* 12px */
        --radius-2xl: 1rem;       /* 16px */
        --radius-3xl: 1.5rem;     /* 24px */
        --radius-full: 9999px;

        /* Professional Shadow System */
        --shadow-xs: 0 0 0 1px rgba(0, 0, 0, 0.05);
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

        /* Refined Transitions */
        --transition-all: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
        --transition-colors: color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1);
        --transition-opacity: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1);
        --transition-shadow: box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1);
        --transition-transform: transform 150ms cubic-bezier(0.4, 0, 0.2, 1);

        /* Z-Index Scale */
        --z-0: 0;
        --z-10: 10;
        --z-20: 20;
        --z-30: 30;
        --z-40: 40;
        --z-50: 50;
        --z-auto: auto;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
        line-height: var(--leading-normal);
        color: var(--text-primary);
        background-color: #ffffff;
        min-height: 100vh;
        font-size: var(--font-size-base);
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
        font-feature-settings: 'kern' 1;
        scroll-behavior: smooth;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1.5rem;
        position: relative;
    }

    /* ===== ACCESSIBILITY ===== */
    .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #000;
        color: #fff;
        padding: 8px;
        text-decoration: none;
        border-radius: 4px;
        z-index: 1000;
        transition: top 0.3s;
    }

    .skip-link:focus {
        top: 6px;
    }

    /* Focus styles for accessibility */
    *:focus {
        outline: 2px solid var(--secondary-color);
        outline-offset: 2px;
    }

    /* ===== HEADER ===== */
    .header {
        background-color: #ffffff;
        border-bottom: 1px solid #e2e8f0;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .header-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem 0;
        gap: 2rem;
    }

    .logo {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .logo-link {
        text-decoration: none;
        color: inherit;
    }

    .logo-link:hover {
        text-decoration: none;
        color: inherit;
    }

    .logo-text {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.5rem;
        font-weight: 700;
        color: #1a202c;
        margin: 0;
    }

    .logo-icon {
        font-size: 1.75rem;
    }

    .logo-subtitle {
        font-size: 0.875rem;
        color: #718096;
        margin: 0;
    }

    .main-nav {
        flex: 1;
        display: flex;
        justify-content: center;
    }

    .nav-list {
        display: flex;
        list-style: none;
        gap: 2rem;
        margin: 0;
        padding: 0;
    }

    .nav-link {
        font-weight: 500;
        color: #4a5568;
        padding: 0.5rem 0;
        transition: color 0.2s ease;
        text-decoration: none;
    }

    .nav-link:hover {
        color: var(--secondary-color);
        text-decoration: none;
    }

    .header-actions {
        display: flex;
        gap: 1rem;
    }

    /* ===== BUTTONS ===== */
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        border: 2px solid transparent;
        border-radius: 8px;
        font-weight: 500;
        font-size: 0.875rem;
        text-decoration: none;
        transition: all 0.2s ease;
        cursor: pointer;
        white-space: nowrap;
    }

    .btn-primary {
        background-color: var(--secondary-color);
        color: white;
        border-color: var(--secondary-color);
    }

    .btn-primary:hover {
        background-color: var(--secondary-light);
        border-color: var(--secondary-light);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background-color: #e2e8f0;
        color: #4a5568;
        border-color: #e2e8f0;
    }

    .btn-secondary:hover {
        background-color: #cbd5e0;
        border-color: #cbd5e0;
        color: #2d3748;
        text-decoration: none;
    }

    /* ===== PAGE LAYOUT ===== */
    .main-content {
        padding: 2rem 0;
    }

    .page-header {
        padding: 2rem 0;
        border-bottom: 1px solid #e2e8f0;
        margin-bottom: 2rem;
        text-align: center;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        color: #1a202c;
    }

    .page-description {
        font-size: 1.25rem;
        color: #4a5568;
        line-height: 1.6;
        max-width: 800px;
        margin: 0 auto 2rem;
    }

    .meta-info {
        display: flex;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #4a5568;
        font-size: 0.875rem;
    }

    .meta-icon {
        font-size: 1rem;
    }

    /* ===== QUICK ACTIONS ===== */
    .quick-actions {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-bottom: 3rem;
        flex-wrap: wrap;
    }

    .action-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 1rem 1.5rem;
        background: #f7fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        text-decoration: none;
        color: var(--secondary-color);
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .action-btn:hover {
        background: #edf2f7;
        transform: translateY(-2px);
        text-decoration: none;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .action-icon {
        font-size: 1.25rem;
    }

    /* ===== DOCUMENTATION GROUPS ===== */
    .documentation-content {
        max-width: 1200px;
        margin: 0 auto;
    }

    .doc-group {
        margin-bottom: 4rem;
    }

    .group-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #e2e8f0;
    }

    .group-title {
        font-size: 2rem;
        font-weight: 700;
        color: #1a202c;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .group-count {
        background: #e2e8f0;
        color: #4a5568;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .file-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
    }

    /* ===== FILE CARDS ===== */
    .file-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        border: 1px solid #e2e8f0;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .file-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .file-card-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 1rem;
        gap: 1rem;
    }

    .file-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1a202c;
        margin: 0;
        flex: 1;
    }

    .file-link {
        color: #1a202c;
        text-decoration: none;
        transition: color 0.2s ease;
    }

    .file-link:hover {
        color: var(--secondary-color);
        text-decoration: none;
    }

    .file-type {
        background: #e2e8f0;
        color: #4a5568;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        flex-shrink: 0;
    }

    .file-meta {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .file-path,
    .file-modified {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #718096;
    }

    .path-icon,
    .modified-icon {
        font-size: 1rem;
    }

    .file-preview {
        color: #4a5568;
        line-height: 1.6;
        margin-bottom: 1.5rem;
        flex: 1;
        font-size: 0.9rem;
    }

    .file-actions {
        margin-top: auto;
    }

    .view-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: var(--secondary-color);
        color: white;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.2s ease;
        font-size: 0.875rem;
    }

    .view-btn:hover {
        background: var(--secondary-light);
        transform: translateY(-1px);
        text-decoration: none;
        color: white;
    }

    .btn-icon {
        font-size: 1rem;
    }

    /* ===== BREADCRUMBS ===== */
    .breadcrumb {
        margin-bottom: 1.5rem;
    }

    .breadcrumb-list {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        list-style: none;
        margin: 0;
        padding: 0;
        font-size: 0.875rem;
    }

    .breadcrumb-list li {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .breadcrumb-list li:not(:last-child)::after {
        content: '→';
        color: #a0aec0;
        font-weight: 500;
    }

    .breadcrumb-list a {
        color: var(--secondary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-list a:hover {
        text-decoration: underline;
    }

    .breadcrumb-list li[aria-current="page"] {
        color: #4a5568;
        font-weight: 500;
    }

    /* ===== FILE META INFO ===== */
    .file-meta-info {
        display: flex;
        gap: 2rem;
        margin-top: 1rem;
        flex-wrap: wrap;
    }

    .meta-text {
        font-weight: 500;
    }

    /* ===== DOCUMENT CONTENT ===== */
    .document-content {
        max-width: 900px;
        margin: 0 auto;
    }

    /* ===== PAGE NAVIGATION ===== */
    .page-nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 1px solid #e2e8f0;
        gap: 1rem;
    }

    .page-nav-link {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        background: #f7fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        text-decoration: none;
        color: var(--secondary-color);
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .page-nav-link:hover {
        background: #edf2f7;
        text-decoration: none;
        transform: translateY(-1px);
    }

    .page-nav-prev {
        margin-right: auto;
    }

    .page-nav-next {
        margin-left: auto;
    }

    /* ===== FOOTER ===== */
    .footer {
        background-color: #f7fafc;
        border-top: 1px solid #e2e8f0;
        margin-top: 4rem;
        padding: 3rem 0 2rem;
    }

    .footer-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .footer-section {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .footer-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1a202c;
        margin: 0;
    }

    .footer-links {
        list-style: none;
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .footer-links a {
        color: #4a5568;
        text-decoration: none;
        font-size: 0.875rem;
        transition: color 0.2s ease;
    }

    .footer-links a:hover {
        color: var(--secondary-color);
        text-decoration: none;
    }

    .footer-bottom {
        padding-top: 2rem;
        border-top: 1px solid #e2e8f0;
        text-align: center;
    }

    .footer-copyright {
        color: #718096;
        font-size: 0.875rem;
        margin: 0;
    }

    /* ===== HUB PAGE STYLES ===== */
    .hero-section {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        padding: 4rem 0;
        margin-bottom: 3rem;
    }

    .hero-content {
        text-align: center;
        max-width: 800px;
        margin: 0 auto;
    }

    .hero-title {
        font-size: 3rem;
        font-weight: 700;
        color: #1a202c;
        margin-bottom: 1.5rem;
    }

    .hero-description {
        font-size: 1.25rem;
        color: #4a5568;
        line-height: 1.6;
        margin-bottom: 3rem;
    }

    .hero-stats {
        display: flex;
        justify-content: center;
        gap: 3rem;
        flex-wrap: wrap;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        display: block;
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--secondary-color);
        line-height: 1;
    }

    .stat-label {
        font-size: 0.875rem;
        color: #718096;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: 500;
    }

    .documentation-types {
        margin-bottom: 4rem;
    }

    .type-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .doc-type-card {
        background: white;
        border-radius: 16px;
        padding: 2.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 2px solid transparent;
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .doc-type-card.primary {
        border-color: var(--secondary-color);
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    }

    .doc-type-card.secondary {
        border-color: #e2e8f0;
    }

    .doc-type-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
    }

    .doc-type-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .doc-type-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .doc-type-title {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1a202c;
        margin-bottom: 0.5rem;
    }

    .doc-type-subtitle {
        color: #718096;
        font-size: 1rem;
        margin: 0;
    }

    .doc-type-content {
        flex: 1;
        margin-bottom: 2rem;
    }

    .doc-type-description {
        color: #4a5568;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .doc-type-features {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .doc-type-features li {
        padding: 0.5rem 0;
        color: #4a5568;
        font-weight: 500;
    }

    .doc-categories {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }

    .category-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f7fafc;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
    }

    .category-item.more {
        justify-content: center;
        color: #718096;
        font-style: italic;
    }

    .category-icon {
        font-size: 1.25rem;
    }

    .category-name {
        flex: 1;
        font-weight: 500;
        color: #4a5568;
    }

    .category-count {
        background: #e2e8f0;
        color: #4a5568;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .doc-type-actions {
        margin-top: auto;
    }

    .btn-large {
        padding: 1rem 2rem;
        font-size: 1rem;
        font-weight: 600;
    }

    .quick-actions-section {
        margin-bottom: 4rem;
    }

    .section-title {
        font-size: 2rem;
        font-weight: 700;
        color: #1a202c;
        text-align: center;
        margin-bottom: 2rem;
    }

    .section-description {
        text-align: center;
        color: #4a5568;
        font-size: 1.125rem;
        margin-bottom: 2rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .quick-action-card {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 2rem;
        text-decoration: none;
        color: inherit;
        transition: all 0.3s ease;
        text-align: center;
    }

    .quick-action-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        text-decoration: none;
        color: inherit;
    }

    .action-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .action-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1a202c;
        margin-bottom: 0.5rem;
    }

    .action-description {
        color: #4a5568;
        font-size: 0.875rem;
        margin: 0;
    }

    .markdown-docs-section {
        background: #f8fafc;
        padding: 4rem 0;
        margin-top: 2rem;
    }

    .section-header {
        text-align: center;
        margin-bottom: 3rem;
    }

    /* ===== RESPONSIVE DESIGN ===== */
    @media (max-width: 768px) {
        .container {
            padding: 0 1rem;
        }

        .header-content {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .main-nav {
            order: 2;
        }

        .header-actions {
            order: 3;
            flex-direction: column;
            width: 100%;
        }

        .nav-list {
            gap: 1rem;
        }

        .hero-section {
            padding: 2rem 0;
        }

        .hero-title {
            font-size: 2.5rem;
        }

        .hero-description {
            font-size: 1.125rem;
        }

        .hero-stats {
            gap: 2rem;
        }

        .type-grid {
            grid-template-columns: 1fr;
        }

        .doc-type-card {
            padding: 2rem;
        }

        .quick-actions-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        .page-title {
            font-size: 2rem;
        }

        .page-description {
            font-size: 1.125rem;
        }

        .meta-info {
            flex-direction: column;
            gap: 1rem;
        }

        .quick-actions {
            flex-direction: column;
            align-items: center;
        }

        .action-btn {
            width: 100%;
            max-width: 300px;
        }

        .file-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .file-card {
            padding: 1.5rem;
        }

        .group-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .page-nav {
            flex-direction: column;
            gap: 1rem;
        }

        .page-nav-link {
            width: 100%;
            text-align: center;
        }

        .footer-content {
            grid-template-columns: 1fr;
            text-align: center;
        }
    }

    @media (max-width: 480px) {
        .hero-title {
            font-size: 2rem;
        }

        .stat-number {
            font-size: 2rem;
        }

        .doc-type-title {
            font-size: 1.5rem;
        }

        .doc-type-card {
            padding: 1.5rem;
        }

        .quick-action-card {
            padding: 1.5rem;
        }

        .page-title {
            font-size: 1.75rem;
        }

        .group-title {
            font-size: 1.5rem;
        }

        .file-card {
            padding: 1rem;
        }

        .btn,
        .action-btn,
        .view-btn {
            padding: 0.625rem 1rem;
            font-size: 0.8rem;
        }

        .btn-large {
            padding: 0.875rem 1.5rem;
            font-size: 0.9rem;
        }
    }

    .subtitle {
        color: var(--text-secondary);
        font-size: var(--font-size-xl);
        margin-bottom: var(--space-6);
        font-weight: 400;
        line-height: var(--leading-relaxed);
        position: relative;
        z-index: var(--z-10);
    }

    .meta {
        display: flex;
        gap: var(--space-4);
        align-items: center;
        flex-wrap: wrap;
        margin-top: var(--space-8);
        padding-top: var(--space-6);
        border-top: 1px solid var(--border-light);
        position: relative;
        z-index: var(--z-10);
    }

    /* Enhanced Button System */
    .refresh-btn, .api-btn, .back-btn {
        background: var(--secondary-color);
        color: white;
        text-decoration: none;
        padding: var(--space-3) var(--space-6);
        border-radius: var(--radius-lg);
        font-size: var(--font-size-sm);
        font-weight: 600;
        transition: var(--transition-all);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-2);
        border: 2px solid transparent;
        cursor: pointer;
        white-space: nowrap;
        position: relative;
        overflow: hidden;
        min-height: 44px; /* Touch target */
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .refresh-btn::before, .api-btn::before, .back-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .refresh-btn:hover::before, .api-btn:hover::before, .back-btn:hover::before {
        left: 100%;
    }

    .refresh-btn:hover, .api-btn:hover, .back-btn:hover {
        background: var(--secondary-light);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        text-decoration: none;
        color: white;
        border-color: var(--secondary-light);
    }

    .refresh-btn:active, .api-btn:active, .back-btn:active {
        transform: translateY(0);
        box-shadow: var(--shadow-base);
    }

    .refresh-btn:focus, .api-btn:focus, .back-btn:focus {
        outline: none;
        box-shadow: var(--shadow-lg), 0 0 0 3px rgba(49, 130, 206, 0.1);
    }

    /* Enhanced Documentation Groups */
    .doc-group {
        background: var(--background-primary);
        padding: var(--space-10);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-lg);
        margin-bottom: var(--space-12);
        border: 1px solid var(--border-light);
        transition: var(--transition-all);
        position: relative;
        overflow: hidden;
    }

    .doc-group::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .doc-group:hover {
        box-shadow: var(--shadow-2xl);
        transform: translateY(-4px);
    }

    .doc-group:hover::before {
        transform: scaleX(1);
    }

    .doc-group h2 {
        color: var(--text-primary);
        margin-bottom: var(--space-8);
        padding-bottom: var(--space-4);
        border-bottom: 2px solid var(--border-light);
        font-size: var(--font-size-3xl);
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: var(--space-3);
        line-height: var(--leading-tight);
        position: relative;
    }

    .doc-group h2::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 60px;
        height: 2px;
        background: var(--secondary-color);
        border-radius: var(--radius-full);
    }

    /* Enhanced File Grid Layout */
    .file-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
        gap: var(--space-8);
        margin-top: var(--space-8);
        align-items: start;
    }

    /* Professional File Card Styling */
    .file-card {
        background: var(--background-primary);
        border: 1px solid var(--border-light);
        border-radius: var(--radius-xl);
        padding: var(--space-8);
        transition: var(--transition-all);
        position: relative;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
        backdrop-filter: blur(10px);
        isolation: isolate;
    }

    .file-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--secondary-color), var(--accent-color), var(--info-color));
        transform: scaleX(0);
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: var(--z-10);
    }

    .file-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at top right, var(--background-accent) 0%, transparent 50%);
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: var(--z-0);
    }

    .file-card:hover {
        transform: translateY(-6px);
        box-shadow: var(--shadow-2xl);
        border-color: var(--secondary-light);
    }

    .file-card:hover::before {
        transform: scaleX(1);
    }

    .file-card:hover::after {
        opacity: 0.4;
    }

    .file-card h3 {
        margin-bottom: var(--space-4);
        font-size: var(--font-size-xl);
        font-weight: 700;
        line-height: var(--leading-tight);
        position: relative;
        z-index: var(--z-10);
    }

    .file-card h3 a {
        color: var(--text-primary);
        text-decoration: none;
        transition: var(--transition-colors);
        display: block;
        position: relative;
    }

    .file-card h3 a::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 2px;
        background: var(--secondary-color);
        transition: width 0.3s ease;
    }

    .file-card h3 a:hover {
        color: var(--secondary-color);
    }

    .file-card h3 a:hover::after {
        width: 100%;
    }

    .file-path {
        color: var(--text-secondary);
        font-size: var(--font-size-xs);
        font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Roboto Mono', monospace;
        margin-bottom: var(--space-3);
        background: var(--background-tertiary);
        padding: var(--space-1_5) var(--space-3);
        border-radius: var(--radius-md);
        display: inline-block;
        border: 1px solid var(--border-medium);
        font-weight: 500;
        letter-spacing: 0.025em;
        position: relative;
        z-index: var(--z-10);
    }

    .file-modified {
        color: var(--text-muted);
        font-size: var(--font-size-xs);
        margin-bottom: var(--space-5);
        display: flex;
        align-items: center;
        gap: var(--space-2);
        font-weight: 500;
        position: relative;
        z-index: var(--z-10);
    }

    .file-modified::before {
        content: '📅';
        font-size: var(--font-size-sm);
        opacity: 0.7;
    }

    .file-preview {
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        line-height: var(--leading-relaxed);
        flex-grow: 1;
        margin: var(--space-5) 0;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        position: relative;
        z-index: var(--z-10);
    }

    .file-actions {
        margin-top: auto;
        padding-top: var(--space-5);
        border-top: 1px solid var(--border-light);
        display: flex;
        gap: var(--space-3);
        align-items: center;
        position: relative;
        z-index: var(--z-10);
    }

    .view-btn {
        background: var(--accent-color);
        color: white;
        text-decoration: none;
        padding: var(--space-3) var(--space-6);
        border-radius: var(--radius-lg);
        font-size: var(--font-size-sm);
        font-weight: 600;
        transition: var(--transition-all);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-2);
        border: 2px solid transparent;
        cursor: pointer;
        min-height: 40px;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        position: relative;
        overflow: hidden;
    }

    .view-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .view-btn:hover::before {
        left: 100%;
    }

    .view-btn:hover {
        background: var(--accent-light);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
        text-decoration: none;
        color: white;
        border-color: var(--accent-light);
    }

    .view-btn:active {
        transform: translateY(0);
        box-shadow: var(--shadow-base);
    }

    .view-btn:focus {
        outline: none;
        box-shadow: var(--shadow-lg), 0 0 0 3px rgba(56, 161, 105, 0.1);
    }

    /* Breadcrumb Navigation */
    .breadcrumb {
        margin-bottom: var(--space-xl);
        padding: var(--space-md);
        background: var(--background-primary);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-light);
        font-size: var(--font-size-sm);
    }

    .breadcrumb a {
        color: var(--secondary-color);
        text-decoration: none;
        transition: color var(--transition-fast);
        font-weight: 500;
    }

    .breadcrumb a:hover {
        color: var(--primary-color);
        text-decoration: underline;
    }

    .breadcrumb span {
        color: var(--text-muted);
        margin: 0 var(--space-sm);
    }

    /* File Meta Information */
    .file-meta h1 {
        margin-bottom: var(--space-md);
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--text-primary);
    }

    .file-info {
        display: flex;
        gap: var(--space-lg);
        flex-wrap: wrap;
        color: var(--text-secondary);
        font-size: var(--font-size-sm);
        align-items: center;
        margin-bottom: var(--space-lg);
        padding: var(--space-md);
        background: var(--background-secondary);
        border-radius: var(--radius-md);
        border: 1px solid var(--border-light);
    }

    .file-info > span {
        display: flex;
        align-items: center;
        gap: var(--space-xs);
    }

    /* Footer Styling */
    footer {
        text-align: center;
        margin-top: var(--space-3xl);
        padding: var(--space-xl);
        color: var(--text-muted);
        background: var(--background-primary);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-light);
        font-size: var(--font-size-sm);
    }

    /* Enhanced Responsive Design */
    @media (max-width: 1280px) {
        .container {
            padding: var(--space-6) var(--space-3);
        }

        .file-grid {
            grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
            gap: var(--space-6);
        }
    }

    @media (max-width: 1024px) {
        .file-grid {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: var(--space-6);
        }

        .doc-group {
            padding: var(--space-8);
        }

        .doc-group h2 {
            font-size: var(--font-size-2xl);
        }
    }

    @media (max-width: 768px) {
        .container {
            padding: var(--space-4) var(--space-3);
        }

        header {
            padding: var(--space-8) var(--space-6);
            margin-bottom: var(--space-8);
        }

        h1 {
            font-size: var(--font-size-4xl);
        }

        .subtitle {
            font-size: var(--font-size-lg);
        }

        .file-grid {
            grid-template-columns: 1fr;
            gap: var(--space-6);
        }

        .meta {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--space-3);
        }

        .file-info {
            flex-direction: column;
            gap: var(--space-3);
            align-items: flex-start;
        }

        .doc-group {
            padding: var(--space-6);
        }

        .doc-group h2 {
            font-size: var(--font-size-xl);
        }

        .file-card {
            padding: var(--space-6);
        }

        .refresh-btn, .api-btn, .back-btn {
            width: 100%;
            justify-content: center;
            margin-bottom: var(--space-2);
        }

        .view-btn {
            width: 100%;
            justify-content: center;
        }
    }

    @media (max-width: 640px) {
        .container {
            padding: var(--space-3) var(--space-2);
        }

        header {
            padding: var(--space-6) var(--space-4);
        }

        h1 {
            font-size: var(--font-size-3xl);
        }

        .subtitle {
            font-size: var(--font-size-base);
        }

        .doc-group {
            padding: var(--space-5);
        }

        .file-card {
            padding: var(--space-5);
        }

        .file-actions {
            flex-direction: column;
            gap: var(--space-3);
        }
    }

    @media (max-width: 480px) {
        .container {
            padding: var(--space-2) var(--space-1);
        }

        header {
            padding: var(--space-5) var(--space-3);
        }

        h1 {
            font-size: var(--font-size-2xl);
        }

        .subtitle {
            font-size: var(--font-size-sm);
        }

        .doc-group {
            padding: var(--space-4);
        }

        .file-card {
            padding: var(--space-4);
        }

        .refresh-btn, .api-btn, .back-btn, .view-btn {
            padding: var(--space-3) var(--space-4);
            font-size: var(--font-size-xs);
        }
    }

    /* Focus and Accessibility */
    .refresh-btn:focus, .api-btn:focus, .back-btn:focus, .view-btn:focus,
    .file-card h3 a:focus, .breadcrumb a:focus {
        outline: 2px solid var(--secondary-color);
        outline-offset: 2px;
    }

    /* Print Styles */
    @media print {
        body {
            background: white;
        }

        .container {
            max-width: none;
            padding: 0;
        }

        header, .doc-group, .file-card {
            box-shadow: none;
            border: 1px solid #ccc;
        }

        .refresh-btn, .api-btn, .back-btn, .view-btn {
            display: none;
        }
    }
</style>`
}

/**
 * Markdown-specific CSS styles
 */
function getMarkdownStyles(): string {
    return `
<style>
    /* Enhanced Markdown Content Container */
    .markdown-content {
        background: var(--background-primary);
        padding: var(--space-16);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-2xl);
        margin-bottom: var(--space-12);
        border: 1px solid var(--border-light);
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
    }

    .markdown-content::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(90deg, var(--secondary-color) 0%, var(--accent-color) 50%, var(--info-color) 100%);
        z-index: var(--z-10);
    }

    .markdown-content::after {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, var(--background-accent) 0%, transparent 70%);
        opacity: 0.2;
        z-index: var(--z-0);
    }

    /* Enhanced Typography Hierarchy */
    .markdown-content h1,
    .markdown-content h2,
    .markdown-content h3,
    .markdown-content h4,
    .markdown-content h5,
    .markdown-content h6 {
        color: var(--text-primary);
        margin-top: var(--space-12);
        margin-bottom: var(--space-6);
        line-height: var(--leading-tight);
        font-weight: 700;
        letter-spacing: -0.025em;
        position: relative;
        z-index: var(--z-10);
    }

    .markdown-content h1:first-child,
    .markdown-content h2:first-child,
    .markdown-content h3:first-child {
        margin-top: 0;
    }

    .markdown-content h1 {
        font-size: var(--font-size-5xl);
        border-bottom: 4px solid var(--secondary-color);
        padding-bottom: var(--space-4);
        font-weight: 800;
        margin-bottom: var(--space-8);
    }

    .markdown-content h2 {
        font-size: var(--font-size-4xl);
        border-bottom: 2px solid var(--border-light);
        padding-bottom: var(--space-3);
        position: relative;
        margin-bottom: var(--space-6);
    }

    .markdown-content h2::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 80px;
        height: 2px;
        background: var(--secondary-color);
        border-radius: var(--radius-full);
    }

    .markdown-content h3 {
        font-size: var(--font-size-3xl);
        color: var(--secondary-color);
        font-weight: 700;
    }

    .markdown-content h4 {
        font-size: var(--font-size-2xl);
        font-weight: 600;
    }

    .markdown-content h5 {
        font-size: var(--font-size-xl);
        font-weight: 600;
    }

    .markdown-content h6 {
        font-size: var(--font-size-lg);
        text-transform: uppercase;
        letter-spacing: 0.1em;
        color: var(--text-secondary);
        font-weight: 600;
    }

    /* Enhanced Text Content */
    .markdown-content p {
        margin-bottom: var(--space-6);
        line-height: var(--leading-relaxed);
        color: var(--text-primary);
        font-size: var(--font-size-base);
        position: relative;
        z-index: var(--z-10);
    }

    .markdown-content p:last-child {
        margin-bottom: 0;
    }

    /* Enhanced Lists */
    .markdown-content ul,
    .markdown-content ol {
        margin-bottom: var(--space-6);
        padding-left: var(--space-8);
        position: relative;
        z-index: var(--z-10);
    }

    .markdown-content li {
        margin-bottom: var(--space-3);
        line-height: var(--leading-relaxed);
        color: var(--text-primary);
    }

    .markdown-content li::marker {
        color: var(--secondary-color);
        font-weight: 600;
    }

    .markdown-content ul ul,
    .markdown-content ol ol,
    .markdown-content ul ol,
    .markdown-content ol ul {
        margin-top: var(--space-2);
        margin-bottom: var(--space-2);
    }

    .markdown-content li > p {
        margin-bottom: var(--space-2);
    }

    /* Enhanced Code Styling */
    .markdown-content code {
        background: var(--background-tertiary);
        padding: var(--space-1) var(--space-2);
        border-radius: var(--radius-md);
        font-family: 'SF Mono', 'Monaco', 'Menlo', 'Consolas', 'Roboto Mono', monospace;
        font-size: var(--font-size-sm);
        color: var(--danger-color);
        border: 1px solid var(--border-medium);
        font-weight: 600;
        letter-spacing: 0.025em;
        position: relative;
        z-index: var(--z-10);
    }

    .markdown-content pre {
        background: var(--primary-color);
        color: #f7fafc;
        padding: var(--space-8);
        border-radius: var(--radius-xl);
        overflow-x: auto;
        margin: var(--space-8) 0;
        border: 1px solid var(--border-dark);
        position: relative;
        box-shadow: var(--shadow-xl);
        z-index: var(--z-10);
    }

    .markdown-content pre::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--secondary-color) 0%, var(--accent-color) 50%, var(--info-color) 100%);
        border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        z-index: var(--z-10);
    }

    .markdown-content pre code {
        background: none;
        color: inherit;
        padding: 0;
        border: none;
        font-size: var(--font-size-sm);
        font-weight: 400;
        letter-spacing: 0;
    }

    .markdown-content pre::-webkit-scrollbar {
        height: 8px;
    }

    .markdown-content pre::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
    }

    .markdown-content pre::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
    }

    .markdown-content pre::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
    }

    /* Blockquotes */
    .markdown-content blockquote {
        border-left: 4px solid var(--secondary-color);
        padding: var(--space-lg) var(--space-xl);
        margin: var(--space-xl) 0;
        background: var(--background-secondary);
        border-radius: 0 var(--radius-md) var(--radius-md) 0;
        color: var(--text-secondary);
        font-style: italic;
        position: relative;
    }

    .markdown-content blockquote::before {
        content: '"';
        position: absolute;
        top: var(--space-sm);
        left: var(--space-md);
        font-size: var(--font-size-3xl);
        color: var(--secondary-color);
        opacity: 0.3;
        font-family: serif;
    }

    .markdown-content blockquote p {
        margin-bottom: var(--space-sm);
    }

    .markdown-content blockquote p:last-child {
        margin-bottom: 0;
    }

    /* Tables */
    .markdown-content table {
        width: 100%;
        border-collapse: collapse;
        margin: var(--space-xl) 0;
        background: var(--background-primary);
        border-radius: var(--radius-lg);
        overflow: hidden;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-light);
    }

    .markdown-content th,
    .markdown-content td {
        padding: var(--space-md) var(--space-lg);
        text-align: left;
        border-bottom: 1px solid var(--border-light);
    }

    .markdown-content th {
        background: var(--background-secondary);
        font-weight: 600;
        color: var(--text-primary);
        font-size: var(--font-size-sm);
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .markdown-content tr:hover {
        background: var(--background-secondary);
    }

    .markdown-content tr:last-child td {
        border-bottom: none;
    }

    /* Links */
    .markdown-content a {
        color: var(--secondary-color);
        text-decoration: none;
        transition: all var(--transition-fast);
        border-bottom: 1px solid transparent;
    }

    .markdown-content a:hover {
        color: var(--primary-color);
        border-bottom-color: var(--secondary-color);
    }

    .markdown-content a:focus {
        outline: 2px solid var(--secondary-color);
        outline-offset: 2px;
        border-radius: var(--radius-sm);
    }

    /* Images */
    .markdown-content img {
        max-width: 100%;
        height: auto;
        border-radius: var(--radius-lg);
        margin: var(--space-xl) 0;
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-light);
    }

    /* Horizontal Rules */
    .markdown-content hr {
        border: none;
        height: 2px;
        background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
        margin: var(--space-2xl) 0;
        border-radius: 1px;
    }

    /* Strong and Emphasis */
    .markdown-content strong {
        font-weight: 600;
        color: var(--text-primary);
    }

    .markdown-content em {
        font-style: italic;
        color: var(--text-secondary);
    }

    /* Enhanced Responsive Markdown Styles */
    @media (max-width: 1024px) {
        .markdown-content {
            padding: var(--space-12);
        }
    }

    @media (max-width: 768px) {
        .markdown-content {
            padding: var(--space-8);
        }

        .markdown-content h1 {
            font-size: var(--font-size-4xl);
        }

        .markdown-content h2 {
            font-size: var(--font-size-3xl);
        }

        .markdown-content h3 {
            font-size: var(--font-size-2xl);
        }

        .markdown-content h4 {
            font-size: var(--font-size-xl);
        }

        .markdown-content pre {
            padding: var(--space-6);
            margin: var(--space-6) 0;
        }

        .markdown-content table {
            font-size: var(--font-size-sm);
        }

        .markdown-content th,
        .markdown-content td {
            padding: var(--space-3);
        }

        .markdown-content blockquote {
            padding: var(--space-4) var(--space-6);
            margin: var(--space-6) 0;
        }
    }

    @media (max-width: 640px) {
        .markdown-content {
            padding: var(--space-6);
        }

        .markdown-content h1 {
            font-size: var(--font-size-3xl);
        }

        .markdown-content h2 {
            font-size: var(--font-size-2xl);
        }

        .markdown-content h3 {
            font-size: var(--font-size-xl);
        }

        .markdown-content ul,
        .markdown-content ol {
            padding-left: var(--space-6);
        }

        .markdown-content pre {
            padding: var(--space-4);
            margin: var(--space-4) 0;
        }

        .markdown-content table {
            display: block;
            overflow-x: auto;
            white-space: nowrap;
            font-size: var(--font-size-xs);
        }

        .markdown-content th,
        .markdown-content td {
            padding: var(--space-2);
        }
    }

    @media (max-width: 480px) {
        .markdown-content {
            padding: var(--space-4);
        }

        .markdown-content h1 {
            font-size: var(--font-size-2xl);
        }

        .markdown-content h2 {
            font-size: var(--font-size-xl);
        }

        .markdown-content h3 {
            font-size: var(--font-size-lg);
        }

        .markdown-content pre {
            padding: var(--space-3);
            font-size: var(--font-size-xs);
        }

        .markdown-content blockquote {
            padding: var(--space-3) var(--space-4);
            margin: var(--space-4) 0;
        }
    }

    /* Dark mode support (future enhancement) */
    @media (prefers-color-scheme: dark) {
        :root {
            --text-primary: #ecf0f1;
            --text-secondary: #bdc3c7;
            --text-muted: #95a5a6;
            --background-primary: #2c3e50;
            --background-secondary: #34495e;
            --background-tertiary: #3e5771;
            --border-light: #4a5f7a;
            --border-medium: #5d6d7e;
        }

        body {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }
    }
</style>`
}
