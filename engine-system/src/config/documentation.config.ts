/**
 * Documentation System Configuration
 * Centralized configuration for the unified documentation system
 */

export interface DocumentationConfig {
    cacheTimeout: number
    maxSearchResults: number
    enableCache: boolean
    enableLogging: boolean
    assetsPath: string
    docsPath: string
    defaultCategory: string
    supportedFileTypes: string[]
    seoConfig: {
        siteName: string
        baseUrl: string
        defaultDescription: string
        defaultKeywords: string[]
    }
    performance: {
        enableCompression: boolean
        cacheHeaders: {
            static: string
            dynamic: string
        }
    }
}

/**
 * Get documentation configuration based on environment
 */
export function getDocumentationConfig(): DocumentationConfig {
    const isDevelopment = process.env.NODE_ENV === 'development'
    const isProduction = process.env.NODE_ENV === 'production'

    return {
        // Cache settings
        cacheTimeout: isDevelopment ? 30 * 1000 : 5 * 60 * 1000, // 30s dev, 5min prod
        enableCache: !isDevelopment, // Disable cache in development
        
        // Search settings
        maxSearchResults: 50,
        
        // Logging
        enableLogging: true,
        
        // Paths
        assetsPath: 'docs-site/assets',
        docsPath: 'docs',
        
        // Content settings
        defaultCategory: 'general',
        supportedFileTypes: ['.md', '.markdown'],
        
        // SEO configuration
        seoConfig: {
            siteName: 'Notification Engine Documentation',
            baseUrl: process.env.BASE_URL || 'http://localhost:3000',
            defaultDescription: 'Comprehensive documentation for the Notification Engine - multi-channel notifications, API integration, and system architecture.',
            defaultKeywords: [
                'notification engine',
                'documentation',
                'api',
                'integration',
                'typescript',
                'express',
                'email',
                'slack',
                'hubspot'
            ]
        },
        
        // Performance settings
        performance: {
            enableCompression: isProduction,
            cacheHeaders: {
                static: isProduction 
                    ? 'public, max-age=86400, immutable' // 24 hours for production
                    : 'no-cache', // No cache for development
                dynamic: isProduction
                    ? 'public, max-age=300' // 5 minutes for production
                    : 'no-cache' // No cache for development
            }
        }
    }
}

/**
 * Validate documentation configuration
 */
export function validateDocumentationConfig(config: DocumentationConfig): void {
    if (config.cacheTimeout < 0) {
        throw new Error('Cache timeout must be non-negative')
    }
    
    if (config.maxSearchResults < 1) {
        throw new Error('Max search results must be at least 1')
    }
    
    if (!config.docsPath) {
        throw new Error('Documentation path is required')
    }
    
    if (!config.assetsPath) {
        throw new Error('Assets path is required')
    }
    
    if (!config.seoConfig.siteName) {
        throw new Error('Site name is required for SEO')
    }
}

/**
 * Get cache headers for different content types
 */
export function getCacheHeaders(contentType: 'static' | 'dynamic'): string {
    const config = getDocumentationConfig()
    return config.performance.cacheHeaders[contentType]
}

/**
 * Check if caching is enabled
 */
export function isCacheEnabled(): boolean {
    return getDocumentationConfig().enableCache
}

/**
 * Check if logging is enabled
 */
export function isLoggingEnabled(): boolean {
    return getDocumentationConfig().enableLogging
}

/**
 * Get SEO metadata for a page
 */
export function getSEOMetadata(title?: string, description?: string, keywords?: string[]) {
    const config = getDocumentationConfig()
    
    return {
        title: title ? `${title} - ${config.seoConfig.siteName}` : config.seoConfig.siteName,
        description: description || config.seoConfig.defaultDescription,
        keywords: keywords ? [...keywords, ...config.seoConfig.defaultKeywords] : config.seoConfig.defaultKeywords,
        siteName: config.seoConfig.siteName,
        baseUrl: config.seoConfig.baseUrl
    }
}

// Export singleton instance
export const documentationConfig = getDocumentationConfig()

// Validate configuration on import
validateDocumentationConfig(documentationConfig)
