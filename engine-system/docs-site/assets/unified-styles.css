/* ===== UNIFIED DOCUMENTATION STYLES ===== */
/* Version: 1.0.0 | Last Updated: 2025-06-23 */
/* Optimized for production use with the Notification Engine documentation system */

/* CSS Reset and Base Styles */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Design System Colors */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-900: #1e3a8a;

    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    --success-500: #10b981;
    --warning-500: #f59e0b;
    --error-500: #ef4444;

    /* Typography */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;

    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Typography */
html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family-sans);
    line-height: 1.6;
    color: var(--gray-900);
    background-color: white;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    margin-bottom: var(--space-4);
    color: var(--gray-900);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: var(--space-4);
    color: var(--gray-700);
}

a {
    color: var(--primary-600);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-700);
    text-decoration: underline;
}

/* Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: var(--space-2);
    background: var(--gray-900);
    color: white;
    padding: var(--space-2) var(--space-4);
    text-decoration: none;
    border-radius: var(--radius-md);
    z-index: 1000;
    transition: top var(--transition-normal);
}

.skip-link:focus {
    top: var(--space-2);
}

*:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* Layout Components */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

/* Header */
.header {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) 0;
    gap: var(--space-8);
}

.logo-link {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    text-decoration: none;
}

.logo-link:hover {
    text-decoration: none;
}

.logo-icon {
    font-size: 1.5rem;
}

.main-nav {
    display: flex;
    gap: var(--space-8);
}

.nav-link {
    font-weight: 500;
    color: var(--gray-600);
    padding: var(--space-2) 0;
    transition: color var(--transition-fast);
}

.nav-link:hover {
    color: var(--primary-600);
    text-decoration: none;
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: var(--space-8) 0;
}

/* Hero Section */
.hero-section {
    text-align: center;
    margin-bottom: var(--space-16);
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: var(--space-6);
    color: var(--gray-900);
}

.hero-description {
    font-size: 1.25rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto var(--space-8);
}

/* Search */
.search-container {
    max-width: 500px;
    margin: 0 auto var(--space-12);
}

.search-form {
    display: flex;
    gap: var(--space-2);
}

.search-input {
    flex: 1;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    transition: border-color var(--transition-fast);
}

.search-input:focus {
    border-color: var(--primary-500);
    outline: none;
}

.search-button {
    padding: var(--space-3) var(--space-6);
    background: var(--primary-600);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-button:hover {
    background: var(--primary-700);
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
    margin-bottom: var(--space-16);
}

.category-card {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.category-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
}

.category-title {
    margin: 0;
    font-size: 1.5rem;
}

.category-title a {
    color: var(--gray-900);
    text-decoration: none;
}

.category-title a:hover {
    color: var(--primary-600);
    text-decoration: none;
}

.category-count {
    background: var(--gray-100);
    color: var(--gray-600);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
}

.category-description {
    color: var(--gray-600);
    margin-bottom: var(--space-6);
}

.category-files {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.file-link {
    color: var(--primary-600);
    font-weight: 500;
    padding: var(--space-2) 0;
    border-bottom: 1px solid transparent;
    transition: all var(--transition-fast);
}

.file-link:hover {
    color: var(--primary-700);
    border-bottom-color: var(--primary-200);
    text-decoration: none;
}

.view-all-link {
    color: var(--gray-500);
    font-size: 0.875rem;
    margin-top: var(--space-2);
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-6);
    padding: var(--space-8);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-600);
}

.stat-label {
    color: var(--gray-600);
    font-weight: 500;
}

/* Content Layout */
.content-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: var(--space-12);
    align-items: start;
}

/* Sidebar */
.sidebar {
    position: sticky;
    top: calc(80px + var(--space-4));
}

.sidebar-nav {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
}

.sidebar-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--space-4);
    color: var(--gray-900);
}

.nav-section {
    margin-bottom: var(--space-6);
}

.nav-section.active {
    background: var(--primary-50);
    border-radius: var(--radius-md);
    padding: var(--space-3);
    margin: 0 calc(-1 * var(--space-3)) var(--space-6);
}

.nav-section-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: var(--space-3);
}

.nav-section-title a {
    color: var(--gray-900);
    text-decoration: none;
}

.nav-section-items {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-section-items li {
    margin-bottom: var(--space-1);
}

.nav-item-link {
    color: var(--gray-600);
    font-size: 0.875rem;
    padding: var(--space-1) 0;
    display: block;
    transition: color var(--transition-fast);
}

.nav-item-link:hover {
    color: var(--primary-600);
    text-decoration: none;
}

/* Content Main */
.content-main {
    min-width: 0; /* Prevent overflow */
}

.content-header {
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
}

.breadcrumb {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin-bottom: var(--space-4);
}

.breadcrumb-link {
    color: var(--primary-600);
}

.breadcrumb-current {
    color: var(--gray-700);
}

.content-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--space-4);
}

.content-description {
    font-size: 1.25rem;
    color: var(--gray-600);
    margin-bottom: var(--space-6);
}

.content-meta {
    display: flex;
    gap: var(--space-6);
    flex-wrap: wrap;
    font-size: 0.875rem;
    color: var(--gray-500);
}

.meta-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

/* Content Body */
.content-body {
    max-width: none;
}

.content-body h2 {
    margin-top: var(--space-12);
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-3);
    border-bottom: 2px solid var(--gray-200);
}

.content-body h3 {
    margin-top: var(--space-10);
    margin-bottom: var(--space-4);
}

.content-body h4 {
    margin-top: var(--space-8);
    margin-bottom: var(--space-3);
}

.content-body ul, .content-body ol {
    margin-bottom: var(--space-6);
    padding-left: var(--space-6);
}

.content-body li {
    margin-bottom: var(--space-2);
}

.content-body code {
    background: var(--gray-100);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-family: var(--font-family-mono);
    font-size: 0.875rem;
    color: var(--gray-800);
}

.content-body pre {
    background: var(--gray-900);
    color: var(--gray-100);
    padding: var(--space-6);
    border-radius: var(--radius-lg);
    overflow-x: auto;
    margin: var(--space-6) 0;
}

.content-body pre code {
    background: none;
    padding: 0;
    color: inherit;
}

.content-body blockquote {
    border-left: 4px solid var(--primary-500);
    padding-left: var(--space-6);
    margin: var(--space-6) 0;
    font-style: italic;
    color: var(--gray-600);
}

.content-body table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--space-6) 0;
}

.content-body th,
.content-body td {
    padding: var(--space-3) var(--space-4);
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.content-body th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-900);
}

/* Footer */
.footer {
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: var(--space-8) 0;
    margin-top: var(--space-20);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--gray-600);
}

.footer-links {
    display: flex;
    gap: var(--space-6);
}

.footer-links a {
    color: var(--gray-500);
    font-size: 0.875rem;
}

.footer-links a:hover {
    color: var(--primary-600);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--space-4);
    }

    .header-content {
        flex-direction: column;
        gap: var(--space-4);
    }

    .main-nav {
        gap: var(--space-4);
    }

    .hero-title {
        font-size: 2rem;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .content-layout {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .sidebar {
        position: static;
        order: 2;
    }

    .quick-stats {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        gap: var(--space-4);
        text-align: center;
    }
}
