<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta
            name="description"
            content="Notification Engine - Comprehensive documentation for multi-channel notification system with TypeScript, Express.js, and queue-based processing"
        />
        <meta
            name="keywords"
            content="notification engine, typescript, express, queue system, email, slack, documentation"
        />
        <title>Notification Engine Documentation</title>
        <link rel="stylesheet" href="assets/styles.css" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
            rel="stylesheet"
        />
    </head>
    <body>
        <!-- Skip to main content for accessibility -->
        <a href="#main-content" class="skip-link">Skip to main content</a>

        <!-- Header Navigation -->
        <header class="header" role="banner">
            <div class="container">
                <div class="header-content">
                    <div class="logo">
                        <h1 class="logo-text">
                            <span class="logo-icon">🔔</span>
                            Notification Engine
                        </h1>
                        <p class="logo-subtitle">Documentation Hub</p>
                    </div>

                    <nav
                        class="main-nav"
                        role="navigation"
                        aria-label="Main navigation"
                    >
                        <ul class="nav-list">
                            <li>
                                <a href="#getting-started" class="nav-link"
                                    >Getting Started</a
                                >
                            </li>
                            <li>
                                <a href="#api-integration" class="nav-link"
                                    >API & Integration</a
                                >
                            </li>
                            <li>
                                <a href="#architecture" class="nav-link"
                                    >Architecture</a
                                >
                            </li>
                            <li>
                                <a href="#operations" class="nav-link"
                                    >Operations</a
                                >
                            </li>
                            <li>
                                <a href="#development" class="nav-link"
                                    >Development</a
                                >
                            </li>
                        </ul>
                    </nav>

                    <div class="header-actions">
                        <a href="/docs" class="btn btn-secondary"
                            >Documentation Hub</a
                        >
                        <a
                            href="overview/project-overview.html"
                            class="btn btn-primary"
                            >Get Started</a
                        >
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main id="main-content" class="main-content" role="main">
            <!-- Hero Section -->
            <section class="hero" aria-labelledby="hero-title">
                <div class="container">
                    <div class="hero-content">
                        <div class="hero-text">
                            <h2 id="hero-title" class="hero-title">
                                Enterprise-Grade Notification Engine
                            </h2>
                            <p class="hero-description">
                                A comprehensive TypeScript-based system for
                                managing multi-channel notifications, trigger
                                evaluations, and template management for SaaS
                                applications. Built with scalability,
                                reliability, and maintainability in mind.
                            </p>

                            <div class="hero-features">
                                <div class="feature-item">
                                    <span class="feature-icon">📧</span>
                                    <span>Multi-Channel Support</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-icon">⚡</span>
                                    <span>Real-Time Processing</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-icon">🔄</span>
                                    <span>Queue-Based Architecture</span>
                                </div>
                                <div class="feature-item">
                                    <span class="feature-icon">📝</span>
                                    <span>Template Engine</span>
                                </div>
                            </div>

                            <div class="hero-actions">
                                <a
                                    href="overview/project-overview.html"
                                    class="btn btn-primary btn-large"
                                >
                                    View Project Overview
                                </a>
                                <a
                                    href="development/development-guide.html"
                                    class="btn btn-outline btn-large"
                                >
                                    Setup Development
                                </a>
                            </div>
                        </div>

                        <div class="hero-visual">
                            <div class="architecture-diagram">
                                <div class="diagram-title">
                                    System Architecture
                                </div>
                                <div class="diagram-content">
                                    <div class="diagram-layer">
                                        <div class="diagram-box api">
                                            API Gateway
                                        </div>
                                        <div class="diagram-box queue">
                                            Queue System
                                        </div>
                                        <div class="diagram-box connectors">
                                            Connectors
                                        </div>
                                    </div>
                                    <div class="diagram-layer">
                                        <div class="diagram-box database">
                                            Database
                                        </div>
                                        <div class="diagram-box templates">
                                            Template Engine
                                        </div>
                                        <div class="diagram-box external">
                                            External Services
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Documentation Sections -->
            <section class="documentation-sections">
                <div class="container">
                    <!-- Getting Started Section -->
                    <section
                        id="getting-started"
                        class="doc-section"
                        aria-labelledby="getting-started-title"
                    >
                        <div class="section-header">
                            <h3
                                id="getting-started-title"
                                class="section-title"
                            >
                                <span class="section-icon">🚀</span>
                                Getting Started
                            </h3>
                            <p class="section-description">
                                Everything you need to understand and start
                                using the Notification Engine
                            </p>
                        </div>

                        <div class="doc-grid">
                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">
                                        Project Overview
                                    </h4>
                                    <span class="doc-card-badge"
                                        >Essential</span
                                    >
                                </div>
                                <p class="doc-card-description">
                                    Complete system overview including
                                    architecture, features, and core concepts
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="overview/project-overview.html"
                                        class="doc-card-link"
                                    >
                                        Read Documentation →
                                    </a>
                                </div>
                            </div>

                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">Quick Setup</h4>
                                    <span class="doc-card-badge">Setup</span>
                                </div>
                                <p class="doc-card-description">
                                    Development environment setup,
                                    prerequisites, and first-time configuration
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="development/development-guide.html"
                                        class="doc-card-link"
                                    >
                                        Setup Environment →
                                    </a>
                                </div>
                            </div>

                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">
                                        Integration Examples
                                    </h4>
                                    <span class="doc-card-badge">Examples</span>
                                </div>
                                <p class="doc-card-description">
                                    Practical integration patterns and
                                    real-world usage examples
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="api/integration-guide.html"
                                        class="doc-card-link"
                                    >
                                        View Examples →
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- API & Integration Section -->
                    <section
                        id="api-integration"
                        class="doc-section"
                        aria-labelledby="api-integration-title"
                    >
                        <div class="section-header">
                            <h3
                                id="api-integration-title"
                                class="section-title"
                            >
                                <span class="section-icon">🔌</span>
                                API & Integration
                            </h3>
                            <p class="section-description">
                                Complete API documentation and integration
                                guides for developers
                            </p>
                        </div>

                        <div class="doc-grid">
                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">
                                        API Reference
                                    </h4>
                                    <span class="doc-card-badge"
                                        >Reference</span
                                    >
                                </div>
                                <p class="doc-card-description">
                                    Complete REST API documentation with
                                    endpoints, schemas, and examples
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="api/api-reference.html"
                                        class="doc-card-link"
                                    >
                                        Browse API →
                                    </a>
                                </div>
                            </div>

                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">
                                        Integration Guide
                                    </h4>
                                    <span class="doc-card-badge">Guide</span>
                                </div>
                                <p class="doc-card-description">
                                    Step-by-step integration examples for email,
                                    Slack, and HubSpot channels
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="api/integration-guide.html"
                                        class="doc-card-link"
                                    >
                                        Integration Guide →
                                    </a>
                                </div>
                            </div>

                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">SDKs & Tools</h4>
                                    <span class="doc-card-badge">Tools</span>
                                </div>
                                <p class="doc-card-description">
                                    Client libraries, utilities, and development
                                    tools for easier integration
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="api/sdks-tools.html"
                                        class="doc-card-link"
                                    >
                                        View Tools →
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- System Architecture Section -->
                    <section
                        id="architecture"
                        class="doc-section"
                        aria-labelledby="architecture-title"
                    >
                        <div class="section-header">
                            <h3 id="architecture-title" class="section-title">
                                <span class="section-icon">🏗️</span>
                                System Architecture
                            </h3>
                            <p class="section-description">
                                Deep dive into system components, database
                                design, and internal architecture
                            </p>
                        </div>

                        <div class="doc-grid">
                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">
                                        Database Schema
                                    </h4>
                                    <span class="doc-card-badge">Schema</span>
                                </div>
                                <p class="doc-card-description">
                                    Complete database models, relationships, and
                                    data management strategies
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="architecture/database.html"
                                        class="doc-card-link"
                                    >
                                        View Schema →
                                    </a>
                                </div>
                            </div>

                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">Queue System</h4>
                                    <span class="doc-card-badge">Queues</span>
                                </div>
                                <p class="doc-card-description">
                                    BullMQ and RabbitMQ architecture, job
                                    processing, and monitoring
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="architecture/queues.html"
                                        class="doc-card-link"
                                    >
                                        Queue Architecture →
                                    </a>
                                </div>
                            </div>

                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">
                                        Template Engine
                                    </h4>
                                    <span class="doc-card-badge"
                                        >Templates</span
                                    >
                                </div>
                                <p class="doc-card-description">
                                    Handlebars templating system, layout
                                    inheritance, and template development
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="architecture/templates.html"
                                        class="doc-card-link"
                                    >
                                        Template System →
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Operations & Deployment Section -->
                    <section
                        id="operations"
                        class="doc-section"
                        aria-labelledby="operations-title"
                    >
                        <div class="section-header">
                            <h3 id="operations-title" class="section-title">
                                <span class="section-icon">⚙️</span>
                                Operations & Deployment
                            </h3>
                            <p class="section-description">
                                Production deployment, testing strategies, and
                                operational best practices
                            </p>
                        </div>

                        <div class="doc-grid">
                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">
                                        Deployment Guide
                                    </h4>
                                    <span class="doc-card-badge">Deploy</span>
                                </div>
                                <p class="doc-card-description">
                                    Production deployment instructions, Docker
                                    setup, and environment configuration
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="operations/deployment.html"
                                        class="doc-card-link"
                                    >
                                        Deploy to Production →
                                    </a>
                                </div>
                            </div>

                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">
                                        Testing Strategies
                                    </h4>
                                    <span class="doc-card-badge">Testing</span>
                                </div>
                                <p class="doc-card-description">
                                    Unit testing, integration testing, and
                                    automated testing best practices
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="operations/testing.html"
                                        class="doc-card-link"
                                    >
                                        Testing Guide →
                                    </a>
                                </div>
                            </div>

                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">
                                        Troubleshooting
                                    </h4>
                                    <span class="doc-card-badge">Support</span>
                                </div>
                                <p class="doc-card-description">
                                    Common issues, debugging techniques, and
                                    performance troubleshooting
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="troubleshooting/troubleshooting-guide.html"
                                        class="doc-card-link"
                                    >
                                        Troubleshoot Issues →
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Development & Contributing Section -->
                    <section
                        id="development"
                        class="doc-section"
                        aria-labelledby="development-title"
                    >
                        <div class="section-header">
                            <h3 id="development-title" class="section-title">
                                <span class="section-icon">👨‍💻</span>
                                Development & Contributing
                            </h3>
                            <p class="section-description">
                                Development workflow, coding standards, and
                                contribution guidelines
                            </p>
                        </div>

                        <div class="doc-grid">
                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">
                                        Development Workflow
                                    </h4>
                                    <span class="doc-card-badge">Workflow</span>
                                </div>
                                <p class="doc-card-description">
                                    Setup instructions, coding standards, and
                                    development best practices
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="development/development-guide.html"
                                        class="doc-card-link"
                                    >
                                        Development Guide →
                                    </a>
                                </div>
                            </div>

                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">
                                        Contributing Guidelines
                                    </h4>
                                    <span class="doc-card-badge"
                                        >Contributing</span
                                    >
                                </div>
                                <p class="doc-card-description">
                                    How to contribute to the project, code
                                    review process, and submission guidelines
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="development/contributing.html"
                                        class="doc-card-link"
                                    >
                                        Contribute →
                                    </a>
                                </div>
                            </div>

                            <div class="doc-card">
                                <div class="doc-card-header">
                                    <h4 class="doc-card-title">
                                        Code Standards
                                    </h4>
                                    <span class="doc-card-badge"
                                        >Standards</span
                                    >
                                </div>
                                <p class="doc-card-description">
                                    TypeScript conventions, code formatting, and
                                    architectural patterns
                                </p>
                                <div class="doc-card-footer">
                                    <a
                                        href="development/code-standards.html"
                                        class="doc-card-link"
                                    >
                                        View Standards →
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </section>

            <!-- Quick Links Section -->
            <section class="quick-links">
                <div class="container">
                    <div class="quick-links-header">
                        <h3 class="quick-links-title">Quick Access</h3>
                        <p class="quick-links-description">
                            Jump directly to commonly accessed documentation
                        </p>
                    </div>

                    <div class="quick-links-grid">
                        <a
                            href="overview/project-overview.html"
                            class="quick-link-item"
                        >
                            <span class="quick-link-icon">📋</span>
                            <span class="quick-link-text"
                                >Project Overview</span
                            >
                        </a>
                        <a
                            href="api/api-reference.html"
                            class="quick-link-item"
                        >
                            <span class="quick-link-icon">📚</span>
                            <span class="quick-link-text">API Reference</span>
                        </a>
                        <a
                            href="development/development-guide.html"
                            class="quick-link-item"
                        >
                            <span class="quick-link-icon">⚡</span>
                            <span class="quick-link-text">Quick Setup</span>
                        </a>
                        <a
                            href="operations/deployment.html"
                            class="quick-link-item"
                        >
                            <span class="quick-link-icon">🚀</span>
                            <span class="quick-link-text">Deploy</span>
                        </a>
                        <a
                            href="troubleshooting/troubleshooting-guide.html"
                            class="quick-link-item"
                        >
                            <span class="quick-link-icon">🔧</span>
                            <span class="quick-link-text">Troubleshoot</span>
                        </a>
                        <a
                            href="api/integration-guide.html"
                            class="quick-link-item"
                        >
                            <span class="quick-link-icon">🔌</span>
                            <span class="quick-link-text">Integration</span>
                        </a>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer" role="contentinfo">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h4 class="footer-title">Documentation</h4>
                        <ul class="footer-links">
                            <li>
                                <a href="overview/project-overview.html"
                                    >Project Overview</a
                                >
                            </li>
                            <li>
                                <a href="api/api-reference.html"
                                    >API Reference</a
                                >
                            </li>
                            <li>
                                <a href="development/development-guide.html"
                                    >Development Guide</a
                                >
                            </li>
                            <li>
                                <a href="operations/deployment.html"
                                    >Deployment</a
                                >
                            </li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4 class="footer-title">Resources</h4>
                        <ul class="footer-links">
                            <li>
                                <a href="api/integration-guide.html"
                                    >Integration Examples</a
                                >
                            </li>
                            <li>
                                <a href="operations/testing.html"
                                    >Testing Guide</a
                                >
                            </li>
                            <li>
                                <a
                                    href="troubleshooting/troubleshooting-guide.html"
                                    >Troubleshooting</a
                                >
                            </li>
                            <li>
                                <a href="development/contributing.html"
                                    >Contributing</a
                                >
                            </li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4 class="footer-title">Support</h4>
                        <ul class="footer-links">
                            <li>
                                <a
                                    href="troubleshooting/troubleshooting-guide.html"
                                    >Common Issues</a
                                >
                            </li>
                            <li>
                                <a href="development/contributing.html"
                                    >Report Bug</a
                                >
                            </li>
                            <li>
                                <a href="development/contributing.html"
                                    >Request Feature</a
                                >
                            </li>
                            <li><a href="#main-content">Back to Top</a></li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4 class="footer-title">Project Info</h4>
                        <p class="footer-text">
                            <strong>Version:</strong> 1.0.0<br />
                            <strong>Last Updated:</strong> June 2025<br />
                            <strong>Maintained by:</strong> Notification Engine
                            Team
                        </p>
                    </div>
                </div>

                <div class="footer-bottom">
                    <p class="footer-copyright">
                        © 2025 Notification Engine Documentation. Built with ❤️
                        for developers.
                    </p>
                </div>
            </div>
        </footer>
    </body>
</html>
